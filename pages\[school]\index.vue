<template>
  <div class="space-y-6">
    <!-- Welcome Header -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            Welcome to {{ schoolContext.school?.name || 'Your School' }}
          </h1>
          <p class="text-gray-600 dark:text-gray-400">
            {{ schoolContext.school?.description || 'Your comprehensive educational management platform' }}
          </p>
          <div class="flex items-center space-x-4 mt-2" v-if="schoolContext.school?.location || schoolContext.school?.subscription_plan">
            <span v-if="schoolContext.school?.location" class="text-sm text-gray-500 dark:text-gray-400">
              📍 {{ schoolContext.school.location }}
            </span>
            <span v-if="schoolContext.school?.subscription_plan" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              {{ schoolContext.school.subscription_plan.charAt(0).toUpperCase() + schoolContext.school.subscription_plan.slice(1) }} Plan
            </span>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-500 dark:text-gray-400">
            {{ currentDate }}
          </span>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Lesson Plans -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:document-text" class="h-8 w-8 text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Lesson Plans</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.lessonPlans }}</p>
          </div>
        </div>
      </div>

      <!-- This Week's Classes -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:calendar-days" class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">This Week's Classes</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.weeklyClasses }}</p>
          </div>
        </div>
      </div>

      <!-- Pending Tasks -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:clipboard-document-list" class="h-8 w-8 text-yellow-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Tasks</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.pendingTasks }}</p>
          </div>
        </div>
      </div>

      <!-- Completed Reflections -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:chat-bubble-left-ellipsis" class="h-8 w-8 text-purple-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Reflections</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.reflections }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Create Lesson Plan -->
      <NuxtLink :to="`/${schoolCode}/rph`"
        class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-md transition-shadow group">
        <div class="flex items-center">
          <Icon name="heroicons:plus-circle"
            class="h-8 w-8 text-blue-600 mr-4 group-hover:scale-110 transition-transform" />
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Create Lesson Plan</h3>
            <p class="text-gray-600 dark:text-gray-400">Start planning your next lesson</p>
          </div>
        </div>
      </NuxtLink>

      <!-- View Schedule -->
      <NuxtLink :to="`/${schoolCode}/jadual-mengajar`"
        class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-md transition-shadow group">
        <div class="flex items-center">
          <Icon name="heroicons:calendar"
            class="h-8 w-8 text-green-600 mr-4 group-hover:scale-110 transition-transform" />
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Teaching Schedule</h3>
            <p class="text-gray-600 dark:text-gray-400">View your weekly timetable</p>
          </div>
        </div>
      </NuxtLink>

      <!-- Manage Classes -->
      <NuxtLink :to="`/${schoolCode}/kelas-subjek`"
        class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-md transition-shadow group">
        <div class="flex items-center">
          <Icon name="heroicons:academic-cap"
            class="h-8 w-8 text-purple-600 mr-4 group-hover:scale-110 transition-transform" />
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Classes & Subjects</h3>
            <p class="text-gray-600 dark:text-gray-400">Manage your classes and subjects</p>
          </div>
        </div>
      </NuxtLink>

      <!-- View Reflections -->
      <NuxtLink :to="`/${schoolCode}/refleksi`"
        class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-md transition-shadow group">
        <div class="flex items-center">
          <Icon name="heroicons:chat-bubble-left-right"
            class="h-8 w-8 text-orange-600 mr-4 group-hover:scale-110 transition-transform" />
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Reflections</h3>
            <p class="text-gray-600 dark:text-gray-400">Review your teaching reflections</p>
          </div>
        </div>
      </NuxtLink>

      <!-- Academic Calendar -->
      <NuxtLink :to="`/${schoolCode}/kalendar-akademik`"
        class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-md transition-shadow group">
        <div class="flex items-center">
          <Icon name="heroicons:calendar-days"
            class="h-8 w-8 text-red-600 mr-4 group-hover:scale-110 transition-transform" />
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Academic Calendar</h3>
            <p class="text-gray-600 dark:text-gray-400">View important dates and events</p>
          </div>
        </div>
      </NuxtLink>

      <!-- Documents -->
      <NuxtLink :to="`/${schoolCode}/dskp`"
        class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-md transition-shadow group">
        <div class="flex items-center">
          <Icon name="heroicons:document-arrow-down"
            class="h-8 w-8 text-teal-600 mr-4 group-hover:scale-110 transition-transform" />
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Documents</h3>
            <p class="text-gray-600 dark:text-gray-400">Access DSKP, RPT, and other documents</p>
          </div>
        </div>
      </NuxtLink>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-medium text-gray-900 dark:text-white">Recent Activity</h2>
      </div>

      <div class="p-6">
        <div v-if="isLoadingActivity" class="space-y-4">
          <div v-for="i in 3" :key="i" class="animate-pulse">
            <div class="flex items-center space-x-4">
              <div class="h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
              <div class="flex-1 space-y-2">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="recentActivity.length === 0" class="text-center py-8">
          <Icon name="heroicons:clock" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No recent activity</h3>
          <p class="text-gray-600 dark:text-gray-400">
            Start using the platform to see your activity here
          </p>
        </div>

        <div v-else class="space-y-4">
          <div v-for="activity in recentActivity" :key="activity.id"
            class="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <div class="flex-shrink-0">
              <Icon :name="activity.icon" class="h-6 w-6 text-gray-600 dark:text-gray-400" />
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ activity.title }}</p>
              <p class="text-sm text-gray-600 dark:text-gray-400">{{ activity.description }}</p>
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              {{ formatDate(activity.timestamp) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// Use school layout
definePageMeta({
  layout: 'school' as any,
  // TEMPORARY: Disable middleware for testing subdomain functionality
  // middleware: 'school-auth' as any
})

// Types
interface School {
  name: string
  description?: string
  location?: string
  established?: number
  subscription_plan?: string
  subscription_status?: string
}

interface SchoolContext {
  school: School | null
  membership: any
  isLoading: boolean
  error: any
}

// Get school context
const route = useRoute()
const schoolCode = computed(() => route.params.school as string)

// Get current subdomain for school detection
const currentSubdomain = useState('currentSubdomain')

// Create dynamic school context based on subdomain
const schoolContext = ref<SchoolContext>({
  school: {
    name: getSchoolNameFromCode(String(currentSubdomain.value) || String(schoolCode.value) || 'demo')
  },
  membership: null,
  isLoading: false,
  error: null
})

// Helper function to get school name from code
function getSchoolNameFromCode(code: string): string {
  const schoolNames: Record<string, string> = {
    'demo': 'Demo School',
    'test': 'Test Academy',
    'school1': 'Sunrise Elementary',
    'school2': 'Future High School',
    'example': 'Innovation College'
  }
  return schoolNames[code] || `${code.charAt(0).toUpperCase() + code.slice(1)} School`
}

// State
const stats = ref({
  lessonPlans: 0,
  weeklyClasses: 0,
  pendingTasks: 0,
  reflections: 0
})

const recentActivity = ref<any[]>([])
const isLoadingActivity = ref(true)

const currentDate = computed(() => {
  return new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

// Composables
const {
  schoolContext: contextData,
  initializeSchoolContext,
  getSchoolLessonPlans,
  getSchoolSubjects,
  isSchoolContext,
  currentSchoolId
} = useSchoolContext()

// Methods
const fetchDashboardData = async () => {
  try {
    // Initialize school context first
    await initializeSchoolContext()

    if (!isSchoolContext.value) {
      console.error('No school context available')
      return
    }

    // Update local school context with the loaded data
    if (contextData.value.school) {
      schoolContext.value.school = {
        name: contextData.value.school.name,
        description: contextData.value.school.description,
        location: contextData.value.school.location,
        established: contextData.value.school.established,
        subscription_plan: contextData.value.school.subscription_plan,
        subscription_status: contextData.value.school.subscription_status
      }

      // Fetch school statistics using the new context system
      await fetchSchoolStats()
    }

    // Fetch recent activity
    await fetchRecentActivity()

  } catch (error) {
    console.error('Error in fetchDashboardData:', error)
    // Fallback to mock data
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
}

// Fetch school statistics using school context
const fetchSchoolStats = async () => {
  try {
    if (!currentSchoolId.value) {
      console.warn('No school ID available for stats')
      return
    }

    // Use the school context helper functions
    const [lessonPlansResult, subjectsResult] = await Promise.all([
      getSchoolLessonPlans(),
      getSchoolSubjects()
    ])

    // Update stats with real data from school context
    stats.value = {
      lessonPlans: lessonPlansResult.data?.length || 0,
      weeklyClasses: (subjectsResult.data?.length || 0) * 5, // Approximate weekly classes
      pendingTasks: Math.floor(Math.random() * 10), // Mock for now
      reflections: Math.floor(Math.random() * 20) // Mock for now
    }

    console.log(`📊 School stats loaded: ${stats.value.lessonPlans} lesson plans, ${subjectsResult.data?.length || 0} subjects`)

  } catch (error) {
    console.error('Error fetching school stats:', error)
    // Fallback to mock data
    stats.value = {
      lessonPlans: 24,
      weeklyClasses: 18,
      pendingTasks: 3,
      reflections: 15
    }
  }
}

// Fetch recent activity (mock data for now)
const fetchRecentActivity = async () => {
  try {
    isLoadingActivity.value = true

    // TODO: Replace with real data from database
    recentActivity.value = [
      {
        id: 1,
        title: 'Created new lesson plan',
        description: 'Mathematics - Algebra Basics',
        icon: 'heroicons:document-plus',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
      },
      {
        id: 2,
        title: 'Updated teaching schedule',
        description: 'Added new time slots for Science class',
        icon: 'heroicons:calendar',
        timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000) // 5 hours ago
      },
      {
        id: 3,
        title: 'Completed reflection',
        description: 'English Literature - Poetry Analysis',
        icon: 'heroicons:chat-bubble-left-ellipsis',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 day ago
      }
    ]

  } catch (error) {
    console.error('Error fetching recent activity:', error)
  } finally {
    isLoadingActivity.value = false
  }
}

const formatDate = (date: Date) => {
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  if (diffInHours < 48) return 'Yesterday'
  return date.toLocaleDateString()
}

// Lifecycle
onMounted(() => {
  fetchDashboardData()
})

// SEO
useHead({
  title: `${schoolContext.value.school?.name || 'School'} Dashboard - RPHMate`,
  meta: [
    {
      name: 'description',
      content: 'Your school dashboard for managing lessons, schedules, and educational activities.'
    }
  ]
})
</script>

<style scoped>
/* Custom styles for school dashboard */
</style>
