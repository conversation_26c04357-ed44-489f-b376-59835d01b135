[{"id": "6c7d6dc8-f779-4daa-bd02-381f1047229c", "created_at": "2025-07-12T13:46:30.102871+00:00", "updated_at": "2025-07-12T13:46:30.102871+00:00", "lesson_plan_id": "29be2215-b68b-4ee4-b141-f53ed0137872", "reflections": {"t4_dfed2e06-f2ce-4c50-b3ce-44576117d6a1_isnin": {"action_items": [], "overall_rating": 5, "time_management": "on_time", "additional_notes": "", "challenges_faced": "", "tidak_terlaksana": null, "tindakan_susulan": [], "resource_adequacy": "adequate", "student_engagement": 5, "improvements_needed": "", "objectives_achieved": true, "successful_strategies": "", "activity_effectiveness": 5, "jumlah_murid_mencapai_objektif": 30}}, "overall_rating": 5, "total_periods": 1, "periods_with_custom_data": 0, "user_id": "35bf1b3c-8965-4aec-9a36-c5573e952cd2"}, {"id": "ffc0b715-2969-4cf0-b353-f511e743af2a", "created_at": "2025-07-12T13:47:49.869233+00:00", "updated_at": "2025-07-12T13:47:49.869233+00:00", "lesson_plan_id": "fcabad7e-ddf2-42e5-ad67-93e562a907d6", "reflections": {"t4_dfed2e06-f2ce-4c50-b3ce-44576117d6a1_isnin": {"action_items": [], "overall_rating": 5, "time_management": "on_time", "additional_notes": "", "challenges_faced": "", "tidak_terlaksana": null, "tindakan_susulan": [], "resource_adequacy": "adequate", "student_engagement": 5, "improvements_needed": "", "objectives_achieved": true, "successful_strategies": "", "activity_effectiveness": 5, "jumlah_murid_mencapai_objektif": 30}}, "overall_rating": 5, "total_periods": 1, "periods_with_custom_data": 0, "user_id": "35bf1b3c-8965-4aec-9a36-c5573e952cd2"}]