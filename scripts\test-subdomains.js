#!/usr/bin/env node

// Subdomain Testing Script
// Created: 2025-07-13
// Purpose: Test subdomain functionality in development

import { execSync } from 'child_process'
import fetch from 'node-fetch'

const DEV_DOMAIN = 'localhost'
const DEV_PORT = '3000'
const BASE_URL = `http://${DEV_DOMAIN}:${DEV_PORT}`

// Sample school codes for testing
const SAMPLE_SCHOOLS = [
  'demo',
  'test',
  'school1',
  'school2',
  'example'
]

class SubdomainTester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    }
  }

  /**
   * Log test result
   */
  logTest(name, passed, message = '') {
    const status = passed ? '✅' : '❌'
    const result = { name, passed, message }
    
    console.log(`${status} ${name}${message ? ': ' + message : ''}`)
    this.results.tests.push(result)
    
    if (passed) {
      this.results.passed++
    } else {
      this.results.failed++
    }
  }

  /**
   * Check if development server is running
   */
  async checkServerRunning() {
    try {
      const response = await fetch(BASE_URL, { 
        timeout: 5000,
        headers: { 'User-Agent': 'SubdomainTester/1.0' }
      })
      return response.status < 500
    } catch (error) {
      return false
    }
  }

  /**
   * Test main domain access
   */
  async testMainDomain() {
    try {
      const response = await fetch(BASE_URL, {
        timeout: 10000,
        headers: { 'User-Agent': 'SubdomainTester/1.0' }
      })
      
      const isSuccess = response.status >= 200 && response.status < 400
      this.logTest(
        'Main domain access',
        isSuccess,
        `Status: ${response.status}`
      )
      
      return isSuccess
    } catch (error) {
      this.logTest(
        'Main domain access',
        false,
        `Error: ${error.message}`
      )
      return false
    }
  }

  /**
   * Test subdomain access
   */
  async testSubdomain(schoolCode) {
    const subdomainUrl = `http://${schoolCode}.${DEV_DOMAIN}:${DEV_PORT}`
    
    try {
      const response = await fetch(subdomainUrl, {
        timeout: 10000,
        headers: { 'User-Agent': 'SubdomainTester/1.0' }
      })
      
      const isSuccess = response.status >= 200 && response.status < 500
      
      // Check for subdomain detection headers
      const detectedSubdomain = response.headers.get('x-detected-subdomain')
      const schoolContext = response.headers.get('x-school-context')
      
      let message = `Status: ${response.status}`
      if (detectedSubdomain) {
        message += `, Detected: ${detectedSubdomain}`
      }
      if (schoolContext) {
        message += `, Context: ${schoolContext}`
      }
      
      this.logTest(
        `Subdomain access (${schoolCode})`,
        isSuccess,
        message
      )
      
      return isSuccess
    } catch (error) {
      this.logTest(
        `Subdomain access (${schoolCode})`,
        false,
        `Error: ${error.message}`
      )
      return false
    }
  }

  /**
   * Test subdomain routing
   */
  async testSubdomainRouting(schoolCode) {
    const routes = [
      '/',
      `/${schoolCode}`,
      `/${schoolCode}/auth/login`,
      '/admin/login' // Should redirect or show error
    ]
    
    for (const route of routes) {
      const subdomainUrl = `http://${schoolCode}.${DEV_DOMAIN}:${DEV_PORT}${route}`
      
      try {
        const response = await fetch(subdomainUrl, {
          timeout: 10000,
          redirect: 'manual', // Don't follow redirects
          headers: { 'User-Agent': 'SubdomainTester/1.0' }
        })
        
        const isSuccess = response.status >= 200 && response.status < 500
        
        this.logTest(
          `Route ${route} on ${schoolCode}`,
          isSuccess,
          `Status: ${response.status}`
        )
      } catch (error) {
        this.logTest(
          `Route ${route} on ${schoolCode}`,
          false,
          `Error: ${error.message}`
        )
      }
    }
  }

  /**
   * Test DNS resolution
   */
  async testDNSResolution() {
    console.log('\n🔍 Testing DNS resolution...')
    
    for (const school of SAMPLE_SCHOOLS) {
      try {
        // Use nslookup or dig to test DNS resolution
        const hostname = `${school}.${DEV_DOMAIN}`
        
        try {
          execSync(`nslookup ${hostname}`, { stdio: 'pipe' })
          this.logTest(`DNS resolution (${hostname})`, true)
        } catch (error) {
          // Try with ping as fallback
          try {
            execSync(`ping -c 1 ${hostname}`, { stdio: 'pipe', timeout: 5000 })
            this.logTest(`DNS resolution (${hostname})`, true, 'Via ping')
          } catch (pingError) {
            this.logTest(`DNS resolution (${hostname})`, false, 'Not resolved')
          }
        }
      } catch (error) {
        this.logTest(`DNS resolution (${school})`, false, error.message)
      }
    }
  }

  /**
   * Test hosts file entries
   */
  testHostsFile() {
    console.log('\n📋 Checking hosts file entries...')
    
    try {
      const { readFileSync } = require('fs')
      const { platform } = require('os')
      
      const hostsFiles = {
        win32: 'C:\\Windows\\System32\\drivers\\etc\\hosts',
        darwin: '/etc/hosts',
        linux: '/etc/hosts'
      }
      
      const hostsFile = hostsFiles[platform()]
      if (!hostsFile) {
        this.logTest('Hosts file check', false, 'Unsupported platform')
        return
      }
      
      const hostsContent = readFileSync(hostsFile, 'utf8')
      
      // Check for RPHMate entries
      const hasRPHMateEntries = hostsContent.includes('# RPHMate Development')
      this.logTest('Hosts file entries', hasRPHMateEntries)
      
      // Check individual school entries
      for (const school of SAMPLE_SCHOOLS) {
        const hasSchoolEntry = hostsContent.includes(`${school}.${DEV_DOMAIN}`)
        this.logTest(`Hosts entry (${school})`, hasSchoolEntry)
      }
      
    } catch (error) {
      this.logTest('Hosts file check', false, error.message)
    }
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 RPHMate Subdomain Testing')
    console.log('================================\n')

    // Check if server is running
    console.log('🔍 Checking development server...')
    const serverRunning = await this.checkServerRunning()
    
    if (!serverRunning) {
      console.log('❌ Development server is not running!')
      console.log('💡 Please start the server with: npm run dev')
      return false
    }
    
    this.logTest('Development server', true, 'Running')

    // Test hosts file
    this.testHostsFile()

    // Test DNS resolution
    await this.testDNSResolution()

    // Test main domain
    console.log('\n🌐 Testing main domain...')
    await this.testMainDomain()

    // Test subdomains
    console.log('\n🏫 Testing school subdomains...')
    for (const school of SAMPLE_SCHOOLS) {
      await this.testSubdomain(school)
    }

    // Test subdomain routing
    console.log('\n🛣️  Testing subdomain routing...')
    for (const school of SAMPLE_SCHOOLS.slice(0, 2)) { // Test first 2 schools
      await this.testSubdomainRouting(school)
    }

    // Print summary
    this.printSummary()
    
    return this.results.failed === 0
  }

  /**
   * Print test summary
   */
  printSummary() {
    console.log('\n📊 Test Summary')
    console.log('===============')
    console.log(`✅ Passed: ${this.results.passed}`)
    console.log(`❌ Failed: ${this.results.failed}`)
    console.log(`📝 Total: ${this.results.tests.length}`)
    
    if (this.results.failed > 0) {
      console.log('\n❌ Failed Tests:')
      this.results.tests
        .filter(test => !test.passed)
        .forEach(test => {
          console.log(`   - ${test.name}: ${test.message}`)
        })
      
      console.log('\n💡 Troubleshooting:')
      console.log('1. Run: node scripts/setup-dev-subdomains.js')
      console.log('2. Restart development server: npm run dev')
      console.log('3. Clear browser cache and DNS cache')
      console.log('4. Check firewall settings')
    } else {
      console.log('\n🎉 All tests passed! Subdomain routing is working correctly.')
    }
  }

  /**
   * Quick test for CI/CD
   */
  async quickTest() {
    const serverRunning = await this.checkServerRunning()
    if (!serverRunning) {
      console.log('❌ Server not running')
      return false
    }

    const mainDomainWorks = await this.testMainDomain()
    const subdomainWorks = await this.testSubdomain('demo')
    
    return mainDomainWorks && subdomainWorks
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2)
  const tester = new SubdomainTester()

  try {
    if (args.includes('--quick') || args.includes('-q')) {
      const success = await tester.quickTest()
      process.exit(success ? 0 : 1)
    } else {
      const success = await tester.runAllTests()
      process.exit(success ? 0 : 1)
    }
  } catch (error) {
    console.error('❌ Testing failed:', error.message)
    process.exit(1)
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { SubdomainTester }
