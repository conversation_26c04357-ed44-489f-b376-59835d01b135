<template>
    <div
        class="fixed inset-0 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-2 sm:p-4 md:p-6 overflow-auto">
        <Card class="w-full max-w-sm sm:max-w-md md:max-w-2xl lg:max-w-4xl overflow-hidden shadow-2xl"
            variant="shadow-2xl" no-default-spacing>
            <div class="flex flex-col lg:flex-row min-h-[400px] sm:min-h-[500px]">
                <!-- Left Panel - Splash/Animation -->
                <div
                    class="lg:w-1/2 bg-gradient-to-br from-primary to-blue-600 flex items-center justify-center p-4 sm:p-6 lg:p-8 relative overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute inset-0 opacity-10">
                        <div class="absolute top-10 left-10 w-20 h-20 bg-white rounded-full animate-pulse"></div>
                        <div
                            class="absolute bottom-20 right-10 w-16 h-16 bg-white rounded-full animate-pulse delay-1000">
                        </div>
                        <div class="absolute top-1/2 left-1/4 w-12 h-12 bg-white rounded-full animate-pulse delay-500">
                        </div>
                    </div> <!-- Main Content -->
                    <div class="text-center text-white z-10">
                        <!-- Educational SVG Animation -->
                        <div class="mb-4 sm:mb-6 lg:mb-8 flex justify-center">
                            <SvgAnimation :size="150" class="sm:w-[200px] sm:h-[200px]" />
                        </div>
                        <h1 class="text-2xl sm:text-3xl font-bold mb-2 sm:mb-4">RPHMate</h1>
                        <p class="text-base sm:text-lg opacity-90 mb-1 sm:mb-2">Streamline Your Teaching Journey</p>
                        <p class="text-xs sm:text-sm opacity-75">Comprehensive lesson planning and educational
                            management system
                        </p>
                    </div>
                </div> <!-- Right Panel - Login Form -->
                <div
                    class="lg:w-1/2 p-4 sm:p-6 lg:p-8 xl:p-12 flex items-center justify-center bg-white dark:bg-gray-900">
                    <div class="w-full max-w-sm space-y-6 sm:space-y-8">
                        <div class="text-center">
                            <h2 class="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-2">Welcome Back
                            </h2>
                            <p class="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Sign in to your account to
                                continue</p>
                        </div>

                        <form @submit.prevent="handleLogin" class="space-y-6">
                            <div class="space-y-4">
                                <div>
                                    <Input id="email" type="email" v-model="email" placeholder="Enter your email"
                                        required autocomplete="email" class="w-full" />
                                </div>
                                <div>
                                    <Input id="password" type="password" v-model="password"
                                        placeholder="Enter your password" required autocomplete="new-password"
                                        class="w-full" />
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <Checkbox id="remember" v-model="rememberMe" />
                                    <label for="remember" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                        Remember me
                                    </label>
                                </div>
                                <a href="#"
                                    class="text-sm text-primary hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300">
                                    Forgot password?
                                </a>
                            </div>

                            <Button type="submit" variant="primary" size="lg" class="w-full" :disabled="loading">
                                {{ loading ? 'Signing in...' : 'Sign In' }}
                            </Button>

                            <p v-if="errorMsg"
                                class="text-red-500 text-sm text-center bg-red-50 dark:bg-red-900/20 p-3 rounded-md">
                                {{ errorMsg }}
                            </p>
                        </form>

                        <div class="space-y-4">
                            <div class="relative">
                                <div class="absolute inset-0 flex items-center">
                                    <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
                                </div>
                                <div class="relative flex justify-center text-sm">
                                    <span class="px-2 bg-white dark:bg-gray-900 text-gray-500 dark:text-gray-400">Or
                                        continue with</span>
                                </div>
                            </div> <Button @click.prevent="handleGoogleLogin" variant="outline" size="lg" class="w-full"
                                :disabled="googleLoading">
                                <div class="flex items-center justify-center space-x-2">
                                    <Icon name="logos:google-icon" class="w-5 h-5 flex-shrink-0" />
                                    <span>{{ googleLoading ? 'Connecting...' : 'Sign in with Google' }}</span>
                                </div>
                            </Button>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                Don't have an account?
                                <a href="/auth/daftar"
                                    class="text-primary hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                                    Sign up here
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </Card>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useSupabase } from '~/composables/useSupabase';
import Button from '~/components/ui/base/Button.vue';
import Input from '~/components/ui/base/Input.vue';
import Card from '~/components/ui/composite/Card.vue';
import Icon from '~/components/ui/base/Icon.vue';
import Checkbox from '~/components/ui/base/Checkbox.vue';
import SvgAnimation from '~/components/ui/base/SvgAnimation.vue';

const { client } = useSupabase();
const router = useRouter();

const email = ref('');
const password = ref('');
const rememberMe = ref(false);
const errorMsg = ref<string | null>(null);
const loading = ref(false);
const googleLoading = ref(false);

const handleLogin = async () => {
    loading.value = true;
    errorMsg.value = null; try {
        const { error } = await client.auth.signInWithPassword({
            email: email.value,
            password: password.value,
        });

        if (error) {
            throw error;
        }

        router.push('/'); // Redirect to the homepage on successful login
    } catch (err: any) {
        if (err.status === 400) {
            // Check if this might be an unconfirmed account
            const pendingEmail = localStorage.getItem('pendingSignupEmail');
            if (pendingEmail === email.value) {
                errorMsg.value = 'Your account was created but needs to complete setup. Redirecting...';
                setTimeout(() => {
                    router.push('/auth/butir-asas');
                }, 2000);
            } else {
                errorMsg.value = 'Invalid email or password.';
            }
        } else if (err.message?.includes('Email not confirmed')) {
            errorMsg.value = 'Please check your email and confirm your account before signing in.';
        } else if (err.message?.includes('Invalid login credentials')) {
            errorMsg.value = 'Invalid email or password. Please check your credentials and try again.';
        } else {
            errorMsg.value = err.message || 'An unexpected error occurred.';
        }
    } finally {
        loading.value = false;
    }
};

const handleGoogleLogin = async () => {
    errorMsg.value = null;
    googleLoading.value = true;
    try {
        const { error } = await client.auth.signInWithOAuth({
            provider: 'google',
            options: {
                redirectTo: `${window.location.origin}/auth/confirm`,
                queryParams: {
                    prompt: 'select_account',
                }
            },
        });
        if (error) {
            throw error;
        }
        // The user will be redirected to Google by Supabase, then back to /auth/confirm
    } catch (err: any) {
        errorMsg.value = err.message || 'An unexpected error occurred with Google Sign-In.';
    } finally {
        googleLoading.value = false;
    }
};

definePageMeta({
    layout: 'blank',
});
</script>

<style scoped>
/* Custom gradient background animation */
@keyframes gradientShift {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

.bg-gradient-animated {
    background: linear-gradient(-45deg, #2563EB, #3B82F6, #1D4ED8, #1E40AF);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}
</style>
