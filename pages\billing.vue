<template>
  <section class="py-20 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          Start Your Free Trial
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-300">
          Get started with RPHMate today. No credit card required for the trial.
        </p>
      </div>

      <!-- Main Form -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
        <div class="grid grid-cols-1 lg:grid-cols-2">
          <!-- Left Side - Form -->
          <div class="p-8">
            <h2 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
              School Information
            </h2>

            <form @submit.prevent="handleSubmit" class="space-y-6">
              <!-- School Details -->
              <div class="space-y-4">
                <div>
                  <label for="schoolName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    School Name *
                  </label>
                  <input 
                    id="schoolName" 
                    v-model="form.schoolName" 
                    type="text" 
                    required 
                    placeholder="Enter your school name"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label for="schoolCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    School Code *
                  </label>
                  <input 
                    id="schoolCode" 
                    v-model="form.schoolCode" 
                    type="text" 
                    required 
                    placeholder="e.g., xba1224"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    @input="validateSchoolCode"
                  />
                  <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    This will be your school's unique URL: {{ form.schoolCode }}.rphmate.com
                  </p>
                  <p v-if="schoolCodeError" class="text-sm text-red-600 mt-1">{{ schoolCodeError }}</p>
                </div>

                <div>
                  <label for="schoolAddress" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    School Address
                  </label>
                  <textarea 
                    id="schoolAddress" 
                    v-model="form.schoolAddress" 
                    rows="3"
                    placeholder="Enter your school address"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  ></textarea>
                </div>
              </div>

              <!-- Admin Details -->
              <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  School Administrator
                </h3>
                
                <div class="space-y-4">
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label for="adminFirstName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        First Name *
                      </label>
                      <input 
                        id="adminFirstName" 
                        v-model="form.adminFirstName" 
                        type="text" 
                        required 
                        placeholder="First name"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>
                    <div>
                      <label for="adminLastName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Last Name *
                      </label>
                      <input 
                        id="adminLastName" 
                        v-model="form.adminLastName" 
                        type="text" 
                        required 
                        placeholder="Last name"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      />
                    </div>
                  </div>

                  <div>
                    <label for="adminEmail" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Email Address *
                    </label>
                    <input 
                      id="adminEmail" 
                      v-model="form.adminEmail" 
                      type="email" 
                      required 
                      placeholder="<EMAIL>"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <div>
                    <label for="adminPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Password *
                    </label>
                    <input 
                      id="adminPassword" 
                      v-model="form.adminPassword" 
                      type="password" 
                      required 
                      placeholder="Create a secure password"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
              </div>

              <!-- Plan Selection -->
              <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Select Plan
                </h3>
                
                <div class="space-y-3">
                  <label class="flex items-center p-4 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                    <input type="radio" v-model="form.selectedPlan" value="basic" class="mr-3" />
                    <div class="flex-1">
                      <div class="font-medium text-gray-900 dark:text-white">Basic - RM99/month</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">Up to 10 teachers</div>
                    </div>
                  </label>
                  
                  <label class="flex items-center p-4 border-2 border-blue-500 rounded-lg cursor-pointer bg-blue-50 dark:bg-blue-900/20">
                    <input type="radio" v-model="form.selectedPlan" value="professional" class="mr-3" checked />
                    <div class="flex-1">
                      <div class="font-medium text-gray-900 dark:text-white">Professional - RM199/month</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">Up to 50 teachers (Most Popular)</div>
                    </div>
                  </label>
                  
                  <label class="flex items-center p-4 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                    <input type="radio" v-model="form.selectedPlan" value="enterprise" class="mr-3" />
                    <div class="flex-1">
                      <div class="font-medium text-gray-900 dark:text-white">Enterprise - RM399/month</div>
                      <div class="text-sm text-gray-500 dark:text-gray-400">Unlimited teachers</div>
                    </div>
                  </label>
                </div>
              </div>

              <!-- Coupon Code -->
              <div>
                <label for="couponCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Coupon Code (Optional)
                </label>
                <input 
                  id="couponCode" 
                  v-model="form.couponCode" 
                  type="text" 
                  placeholder="Enter coupon code"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <!-- Error Message -->
              <div v-if="errorMessage" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                <div class="flex">
                  <UiBaseIcon name="heroicons:exclamation-triangle" class="h-5 w-5 text-red-400" />
                  <div class="ml-3">
                    <p class="text-sm text-red-800 dark:text-red-200">{{ errorMessage }}</p>
                  </div>
                </div>
              </div>

              <!-- Submit Button -->
              <button
                type="submit"
                :disabled="isSubmitting || !!schoolCodeError"
                class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-3 px-6 rounded-lg font-semibold transition-colors"
              >
                {{ isSubmitting ? 'Creating Account...' : 'Start Free Trial' }}
              </button>

              <!-- Terms -->
              <p class="text-sm text-gray-500 dark:text-gray-400 text-center">
                By clicking "Start Free Trial", you agree to our 
                <a href="#" class="text-blue-600 hover:text-blue-700">Terms of Service</a> and 
                <a href="#" class="text-blue-600 hover:text-blue-700">Privacy Policy</a>.
              </p>
            </form>
          </div>

          <!-- Right Side - Summary -->
          <div class="bg-gray-50 dark:bg-gray-700 p-8">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-6">
              Order Summary
            </h3>

            <div class="space-y-4">
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-300">Plan</span>
                <span class="font-medium text-gray-900 dark:text-white">
                  {{ planDetails[form.selectedPlan]?.name || 'Professional' }}
                </span>
              </div>
              
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-300">Monthly Price</span>
                <span class="font-medium text-gray-900 dark:text-white">
                  RM{{ planDetails[form.selectedPlan]?.price || '199' }}
                </span>
              </div>
              
              <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                <div class="flex justify-between text-lg font-semibold">
                  <span class="text-gray-900 dark:text-white">Free Trial</span>
                  <span class="text-green-600 dark:text-green-400">1 Month</span>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  No payment required now. Billing starts after trial ends.
                </p>
              </div>
            </div>

            <!-- Features -->
            <div class="mt-8">
              <h4 class="font-medium text-gray-900 dark:text-white mb-4">What's included:</h4>
              <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                <li class="flex items-center">
                  <UiBaseIcon name="heroicons:check" class="h-4 w-4 text-green-500 mr-2" />
                  {{ planDetails[form.selectedPlan]?.teachers || 'Up to 50 teachers' }}
                </li>
                <li class="flex items-center">
                  <UiBaseIcon name="heroicons:check" class="h-4 w-4 text-green-500 mr-2" />
                  Unlimited lesson plans
                </li>
                <li class="flex items-center">
                  <UiBaseIcon name="heroicons:check" class="h-4 w-4 text-green-500 mr-2" />
                  Advanced analytics
                </li>
                <li class="flex items-center">
                  <UiBaseIcon name="heroicons:check" class="h-4 w-4 text-green-500 mr-2" />
                  Priority support
                </li>
                <li class="flex items-center">
                  <UiBaseIcon name="heroicons:check" class="h-4 w-4 text-green-500 mr-2" />
                  1-month free trial
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  layout: 'landing'
})

// Form state
const form = ref({
  schoolName: '',
  schoolCode: '',
  schoolAddress: '',
  adminFirstName: '',
  adminLastName: '',
  adminEmail: '',
  adminPassword: '',
  selectedPlan: 'professional',
  couponCode: ''
})

const isSubmitting = ref(false)
const schoolCodeError = ref('')
const errorMessage = ref('')

// Plan details
const planDetails: Record<string, { name: string; price: string; teachers: string }> = {
  basic: { name: 'Basic', price: '99', teachers: 'Up to 10 teachers' },
  professional: { name: 'Professional', price: '199', teachers: 'Up to 50 teachers' },
  enterprise: { name: 'Enterprise', price: '399', teachers: 'Unlimited teachers' }
}

// Validate school code
const validateSchoolCode = () => {
  const code = form.value.schoolCode.toLowerCase()
  if (code.length < 3) {
    schoolCodeError.value = 'School code must be at least 3 characters'
  } else if (!/^[a-z0-9]+$/.test(code)) {
    schoolCodeError.value = 'School code can only contain letters and numbers'
  } else {
    schoolCodeError.value = ''
  }
}

// Handle form submission
const handleSubmit = async () => {
  if (schoolCodeError.value) return

  isSubmitting.value = true
  errorMessage.value = ''

  try {
    // Create Stripe checkout session
    const response = await $fetch('/api/stripe/create-checkout', {
      method: 'POST',
      body: {
        schoolName: form.value.schoolName,
        schoolCode: form.value.schoolCode,
        schoolAddress: form.value.schoolAddress,
        adminFirstName: form.value.adminFirstName,
        adminLastName: form.value.adminLastName,
        adminEmail: form.value.adminEmail,
        selectedPlan: form.value.selectedPlan,
        couponCode: form.value.couponCode
      }
    }) as any

    if (response.success && response.url) {
      // Redirect to Stripe checkout
      window.location.href = response.url
    } else {
      throw new Error('Failed to create checkout session')
    }

  } catch (error: any) {
    console.error('Registration error:', error)
    errorMessage.value = error.data?.message || error.message || 'Registration failed. Please try again.'
  } finally {
    isSubmitting.value = false
  }
}

// Set page head
useHead({
  title: 'Start Your Free Trial - RPHMate',
  meta: [
    {
      name: 'description',
      content: 'Start your free trial with RPHMate. Create your school account and get access to comprehensive educational tools.'
    }
  ]
})
</script>
