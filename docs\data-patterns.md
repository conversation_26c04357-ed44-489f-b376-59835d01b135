# Multi-Tenant Data Patterns

## Overview

This document defines the data access patterns for the RPHMate multi-tenant SaaS educational platform. It establishes which data is shared globally across all schools and which data is isolated per school.

**Architecture Context:**
- Main domain (`rphmate.com`): Landing pages, pricing, billing, school admin management
- School subdomains (`schoolcode.rphmate.com`): School-specific application access
- Complete data isolation between schools with shared global resources

## Data Classification

### 🌍 Global Data (Shared Across All Schools)

#### 1. **System Configuration**
- **Tables**: `system_settings`, `feature_flags`
- **Access**: Read-only for all users, write access for super admins only
- **Purpose**: Platform-wide configuration and feature toggles

#### 2. **Global Subjects**
- **Tables**: `subjects` (where `school_id IS NULL`)
- **Access**: Read access for all authenticated users
- **Purpose**: Standard curriculum subjects (Mathematics, Science, English, etc.)
- **Management**: Maintained by platform administrators

#### 3. **Global Templates**
- **Tables**: `templates` (where `school_id IS NULL`)
- **Access**: Read access for all authenticated users
- **Purpose**: Standard lesson plan templates, assessment templates
- **Management**: Curated by platform team

#### 4. **Global Documents**
- **Tables**: `documents` (where `school_id IS NULL`)
- **Access**: Read access for all authenticated users
- **Purpose**: Curriculum standards (DSKP), national guidelines, reference materials
- **Management**: Uploaded and maintained by platform administrators

#### 5. **User Authentication & Profiles**
- **Tables**: `auth.users`, `profiles`
- **Access**: Users can only access their own profile data
- **Purpose**: User authentication, personal information, preferences
- **Note**: Users can belong to multiple schools

#### 6. **Coupon System**
- **Tables**: `coupons`, `coupon_usage`
- **Access**:
  - `coupons`: Super admin access only
  - `coupon_usage`: School admins can view their school's usage
- **Purpose**: Platform-wide promotional codes and usage tracking

#### 7. **Payment & Billing System** ✅ IMPLEMENTED
- **Tables**: `subscription_plans`, `payment_history`, `invoices`, `stripe_customers`
- **Access**:
  - `subscription_plans`: Read access for all users, admin write access
  - `payment_history`: School admins can view their school's history only
  - `invoices`: School admins can view their school's invoices only
  - `stripe_customers`: System access only for webhook processing
- **Purpose**: Stripe integration, subscription management, billing history
- **Implementation**: Webhook handler processes payments and creates school accounts automatically

#### 8. **Platform Analytics**
- **Tables**: `platform_analytics`, `usage_metrics`
- **Access**: Super admin access only
- **Purpose**: Platform-wide usage statistics, performance metrics

### 🏫 School-Specific Data (Isolated Per School)

#### 1. **School Management**
- **Tables**: `schools`, `school_memberships`
- **Access**: Users can only access schools they're members of
- **Purpose**: School information, user-school relationships, roles

#### 2. **Educational Content**
- **Tables**: 
  - `lesson_plans` (with `school_id`)
  - `teaching_schedules` (with `school_id`)
  - `timetables` (with `school_id`)
  - `reflections` (with `school_id`)
- **Access**: Only accessible to school members
- **Purpose**: School-specific educational activities and planning

#### 3. **School Structure**
- **Tables**: 
  - `classes` (with `school_id`)
  - `subjects` (with `school_id` - school-specific subjects)
- **Access**: Only accessible to school members
- **Purpose**: School's organizational structure

#### 4. **School Resources**
- **Tables**: 
  - `templates` (with `school_id` - custom templates)
  - `documents` (with `school_id` - school-specific documents)
- **Access**: Only accessible to school members
- **Purpose**: School-created or customized resources

#### 5. **School Calendar**
- **Tables**: `academic_calendars` (with `school_id`)
- **Access**: Only accessible to school members
- **Purpose**: School-specific academic calendar and events

## Access Patterns

### 1. **Global Data Access Pattern**

```typescript
// Example: Fetching global subjects
const { data: globalSubjects } = await supabase
  .from('subjects')
  .select('*')
  .is('school_id', null) // Global subjects only

// Example: Fetching global templates
const { data: globalTemplates } = await supabase
  .from('templates')
  .select('*')
  .is('school_id', null)
```

### 2. **School-Specific Data Access Pattern**

```typescript
// Example: Fetching school-specific lesson plans
const { data: schoolLessonPlans } = await supabase
  .from('lesson_plans')
  .select('*')
  .eq('school_id', currentSchoolId)

// Example: Fetching combined subjects (global + school-specific)
const { data: allSubjects } = await supabase
  .from('subjects')
  .select('*')
  .or(`school_id.is.null,school_id.eq.${currentSchoolId}`)
```

### 3. **Multi-School User Access Pattern**

```typescript
// Example: Fetching user's accessible schools
const { data: userSchools } = await supabase
  .from('school_memberships')
  .select(`
    school_id,
    role,
    schools (
      id,
      name,
      code
    )
  `)
  .eq('user_id', userId)
  .eq('status', 'active')
```

## Data Inheritance Hierarchy

### Subject Hierarchy
1. **Global Subjects** (Base curriculum)
   - Mathematics, Science, English, etc.
   - Cannot be modified by schools
   - Available to all schools

2. **School-Specific Subjects** (Extensions)
   - Custom subjects created by schools
   - School-specific variations
   - Only available to that school

### Template Hierarchy
1. **Global Templates** (Standard templates)
   - Lesson plan templates
   - Assessment templates
   - Created by platform team

2. **School Templates** (Customized templates)
   - Modified versions of global templates
   - School-specific templates
   - Created by school staff

## Security Considerations

### Row Level Security (RLS)
- All school-specific tables have RLS policies
- Users can only access data from their associated schools
- Global data has read-only access for authenticated users

### API Security
- All API endpoints validate school access
- User permissions checked at application level
- Audit trails for sensitive operations

### Data Isolation
- Complete separation between schools
- No cross-school data leakage
- Proper indexing for performance

## Performance Optimization

### Indexing Strategy
```sql
-- School-specific data indexes
CREATE INDEX idx_lesson_plans_school_user ON lesson_plans(school_id, user_id);
CREATE INDEX idx_teaching_schedules_school_user ON teaching_schedules(school_id, user_id);
CREATE INDEX idx_school_memberships_user_school ON school_memberships(user_id, school_id);

-- Global data indexes
CREATE INDEX idx_subjects_global ON subjects(id) WHERE school_id IS NULL;
CREATE INDEX idx_templates_global ON templates(id) WHERE school_id IS NULL;
```

### Caching Strategy
- Global data: Long-term caching (24 hours)
- School-specific data: Short-term caching (1 hour)
- User-specific data: Session-based caching

## Migration Considerations

### Existing Data Migration
1. **Identify Data Type**: Determine if existing data should be global or school-specific
2. **Default School Assignment**: Assign existing data to a default school during migration
3. **Global Data Extraction**: Extract commonly used data to global tables
4. **User Migration**: Migrate existing users to the new school membership system

### Data Cleanup
1. **Duplicate Removal**: Remove duplicate subjects/templates across schools
2. **Global Promotion**: Promote commonly used school-specific items to global
3. **Orphaned Data**: Clean up data without proper school associations

## Monitoring and Auditing

### Access Monitoring
- Log all cross-school access attempts
- Monitor RLS policy violations
- Track global data modifications

### Performance Monitoring
- Query performance by school size
- Index usage statistics
- Cache hit rates

### Data Growth Tracking
- School-specific data growth rates
- Global data usage patterns
- Storage optimization opportunities

## Best Practices

### Development
1. Always include school context in queries
2. Use the `useMultiTenant` composable for data access
3. Test with multiple schools and users
4. Validate RLS policies in development

### Deployment
1. Apply RLS policies before data migration
2. Test data isolation thoroughly
3. Monitor performance after deployment
4. Have rollback procedures ready

### Maintenance
1. Regular RLS policy audits
2. Performance optimization reviews
3. Data cleanup procedures
4. Security assessments
