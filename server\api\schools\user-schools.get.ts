// Get user schools API endpoint
// Created: 2025-07-13

import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    // Get authorization header
    const authHeader = getHeader(event, 'authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing or invalid authorization header'
      })
    }

    const token = authHeader.replace('Bearer ', '')

    // Initialize Supabase client with the user's token
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: authHeader
          }
        }
      }
    )

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired token'
      })
    }

    // Try to get user's school memberships with school data
    let schools: any[] = []

    try {
      const { data: memberships, error: membershipError } = await supabase
        .from('school_memberships')
        .select(`
          *,
          school:schools(*)
        `)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .order('joined_at', { ascending: false })

      if (!membershipError && memberships) {
        // Transform the data
        schools = memberships.map(membership => ({
          ...membership.school,
          membership: {
            id: membership.id,
            role: membership.role,
            status: membership.status,
            joined_at: membership.joined_at,
            permissions: membership.permissions
          }
        }))
      }
    } catch (error) {
      console.log('School memberships table not available, checking admin schools only')
    }

    // Get admin schools (schools where user is the admin_user_id)
    try {
      const { data: adminSchools, error: adminError } = await supabase
        .from('schools')
        .select('*')
        .eq('admin_user_id', user.id)

      if (!adminError && adminSchools) {
        // Add admin schools that might not have memberships
        for (const adminSchool of adminSchools) {
          const existingSchool = schools.find(s => s.id === adminSchool.id)
          if (!existingSchool) {
            schools.push({
              ...adminSchool,
              membership: {
                id: null,
                role: 'admin',
                status: 'active',
                joined_at: adminSchool.created_at,
                permissions: {}
              }
            })
          }
        }
      }
    } catch (error) {
      console.log('Schools table not available, using development fallback')
    }

    // Development fallback: If no schools found and we're in development, create mock data
    if (schools.length === 0 && process.env.NODE_ENV === 'development') {
      // Check if this user should have access to demo schools based on email
      const demoSchools = [
        {
          id: 'demo-school-1',
          name: 'Demo High School',
          code: 'demo',
          admin_user_id: user.id,
          subscription_status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          subscription_expires_at: null,
          description: 'Demo school for testing',
          contact_email: user.email,
          contact_phone: null,
          address: '123 Demo Street, Demo City',
          settings: {},
          membership: {
            id: null,
            role: 'admin',
            status: 'active',
            joined_at: new Date().toISOString(),
            permissions: {}
          }
        }
      ]

      schools = demoSchools
    }

    return {
      success: true,
      schools,
      total: schools.length
    }

  } catch (error: any) {
    console.error('User schools API error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
