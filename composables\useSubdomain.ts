// Subdomain utilities composable
// Created: 2025-07-13
// Purpose: Provide utilities for subdomain detection and navigation

export interface SubdomainInfo {
  current: string | null
  isSchoolContext: boolean
  schoolCode: string | null
  baseDomain: string
  fullDomain: string
}

export interface SchoolContext {
  schoolCode: string
  isSchoolContext: boolean
  detectedAt: string
}

export const useSubdomain = () => {
  const config = useRuntimeConfig()
  const router = useRouter()
  
  // Get current subdomain from state
  const currentSubdomain = useState<string | null>('currentSubdomain', () => null)
  const schoolContext = useState<SchoolContext | null>('schoolContext', () => null)
  
  // Get base domain from config
  const baseDomain = computed(() => {
    return config.public.baseDomain || 'localhost:3000'
  })
  
  // Get current full domain
  const fullDomain = computed(() => {
    if (process.client) {
      return window.location.host
    }
    return baseDomain.value
  })
  
  // Computed subdomain info
  const subdomainInfo = computed((): SubdomainInfo => {
    return {
      current: currentSubdomain.value,
      isSchoolContext: !!currentSubdomain.value,
      schoolCode: currentSubdomain.value,
      baseDomain: baseDomain.value,
      fullDomain: fullDomain.value
    }
  })
  
  // Check if we're in a school context
  const isSchoolContext = computed(() => {
    return !!currentSubdomain.value
  })
  
  // Get current school code
  const currentSchoolCode = computed(() => {
    return currentSubdomain.value
  })
  
  // Navigation utilities
  const navigation = {
    // Get URL for a school
    getSchoolUrl: (schoolCode: string, path: string = '/'): string => {
      if (process.client) {
        const protocol = window.location.protocol
        return `${protocol}//${schoolCode}.${baseDomain.value}${path}`
      }
      return `https://${schoolCode}.${baseDomain.value}${path}`
    },
    
    // Get URL for main domain
    getMainUrl: (path: string = '/'): string => {
      if (process.client) {
        const protocol = window.location.protocol
        return `${protocol}//${baseDomain.value}${path}`
      }
      return `https://${baseDomain.value}${path}`
    },
    
    // Navigate to a school (client-side only)
    goToSchool: (schoolCode: string, path: string = '/') => {
      if (process.client) {
        const url = navigation.getSchoolUrl(schoolCode, path)
        window.location.href = url
      }
    },
    
    // Navigate to main domain (client-side only)
    goToMain: (path: string = '/') => {
      if (process.client) {
        const url = navigation.getMainUrl(path)
        window.location.href = url
      }
    },
    
    // Navigate within current school context
    goToSchoolPage: (path: string) => {
      if (currentSchoolCode.value) {
        router.push(`/${currentSchoolCode.value}${path}`)
      }
    },
    
    // Check if a URL belongs to the current school
    isCurrentSchoolUrl: (url: string): boolean => {
      if (!currentSchoolCode.value) return false
      return url.includes(`${currentSchoolCode.value}.${baseDomain.value}`)
    },
    
    // Extract school code from URL
    extractSchoolCode: (url: string): string | null => {
      try {
        const urlObj = new URL(url)
        const hostname = urlObj.hostname
        
        if (hostname.includes('localhost')) {
          const parts = hostname.split('.')
          if (parts.length > 1 && parts[0] !== 'localhost') {
            return parts[0]
          }
        } else {
          const baseParts = baseDomain.value.split('.')
          const hostParts = hostname.split('.')
          
          if (hostParts.length > baseParts.length) {
            const subdomainParts = hostParts.slice(0, hostParts.length - baseParts.length)
            return subdomainParts.join('.')
          }
        }
      } catch (error) {
        console.error('Error extracting school code from URL:', error)
      }
      
      return null
    }
  }
  
  // School context utilities
  const context = {
    // Set school context (for testing or manual override)
    setSchoolContext: (schoolCode: string) => {
      currentSubdomain.value = schoolCode
      schoolContext.value = {
        schoolCode,
        isSchoolContext: true,
        detectedAt: new Date().toISOString()
      }
    },
    
    // Clear school context
    clearSchoolContext: () => {
      currentSubdomain.value = null
      schoolContext.value = null
    },
    
    // Get current school context
    getSchoolContext: (): SchoolContext | null => {
      return schoolContext.value
    },
    
    // Validate school code format
    isValidSchoolCode: (code: string): boolean => {
      // School codes should be lowercase alphanumeric, 3-20 characters
      return /^[a-z0-9]{3,20}$/.test(code)
    },
    
    // Check if school code is reserved
    isReservedSchoolCode: (code: string): boolean => {
      const reserved = [
        'www', 'api', 'admin', 'app', 'mail', 'ftp', 'blog', 'shop', 'store',
        'support', 'help', 'docs', 'dev', 'test', 'staging', 'prod', 'production',
        'dashboard', 'panel', 'control', 'manage', 'system', 'root', 'server'
      ]
      return reserved.includes(code.toLowerCase())
    }
  }
  
  // Development utilities
  const dev = {
    // Get sample school codes for development
    getSampleSchools: (): string[] => {
      const sampleSchools = config.public.devSampleSchools
      if (typeof sampleSchools === 'string') {
        return sampleSchools.split(',').map(s => s.trim())
      }
      return ['demo', 'test', 'school1', 'school2', 'example']
    },
    
    // Check if we're in development mode
    isDevelopment: (): boolean => {
      return config.public.devMode === 'true' || process.dev
    },
    
    // Get development URLs for testing
    getDevUrls: () => {
      const sampleSchools = dev.getSampleSchools()
      return {
        main: navigation.getMainUrl(),
        schools: sampleSchools.map(school => ({
          code: school,
          url: navigation.getSchoolUrl(school),
          dashboard: navigation.getSchoolUrl(school, `/${school}`)
        }))
      }
    }
  }
  
  // Reactive watchers
  watch(currentSubdomain, (newSubdomain) => {
    if (newSubdomain) {
      console.log(`🏫 School context changed to: ${newSubdomain}`)
    } else {
      console.log('🌐 Switched to main domain context')
    }
  })
  
  return {
    // State
    subdomainInfo: readonly(subdomainInfo),
    isSchoolContext: readonly(isSchoolContext),
    currentSchoolCode: readonly(currentSchoolCode),
    schoolContext: readonly(schoolContext),
    
    // Utilities
    navigation,
    context,
    dev
  }
}
