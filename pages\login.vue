<template>
  <section class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <NuxtLink to="/" class="flex items-center justify-center space-x-2 mb-6">
          <UiBaseIcon name="heroicons:academic-cap" class="h-12 w-12 text-blue-600" />
          <span class="text-2xl font-bold text-gray-900 dark:text-white">RPHMate</span>
        </NuxtLink>
        <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white">
          School Admin Sign In
        </h2>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          Access your school management dashboard
        </p>
      </div>

      <!-- Login Form -->
      <form @submit.prevent="handleSubmit" class="mt-8 space-y-6">
        <div class="space-y-4">
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Email Address
            </label>
            <input 
              id="email" 
              v-model="form.email" 
              type="email" 
              required 
              placeholder="Enter your email"
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Password
            </label>
            <div class="mt-1 relative">
              <input 
                id="password" 
                v-model="form.password" 
                :type="showPassword ? 'text' : 'password'" 
                required
                placeholder="Enter your password"
                class="appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
              <button 
                type="button" 
                @click="showPassword = !showPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <UiBaseIcon 
                  :name="showPassword ? 'heroicons:eye-slash' : 'heroicons:eye'" 
                  class="h-5 w-5 text-gray-400 hover:text-gray-600"
                />
              </button>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input 
                id="remember-me" 
                v-model="form.rememberMe" 
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label for="remember-me" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Remember me
              </label>
            </div>

            <div class="text-sm">
              <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
                Forgot your password?
              </a>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div v-if="errorMessage" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div class="flex">
            <UiBaseIcon name="heroicons:exclamation-triangle" class="h-5 w-5 text-red-400" />
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">{{ errorMessage }}</p>
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <div>
          <button 
            type="submit" 
            :disabled="isLoading"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-400 disabled:cursor-not-allowed"
          >
            <span v-if="isLoading" class="absolute left-0 inset-y-0 flex items-center pl-3">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            </span>
            {{ isLoading ? 'Signing in...' : 'Sign in' }}
          </button>
        </div>

        <!-- Sign Up Link -->
        <div class="text-center">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Don't have a school account?
            <NuxtLink to="/pricing" class="font-medium text-blue-600 hover:text-blue-500">
              Start your free trial
            </NuxtLink>
          </p>
        </div>

        <!-- Divider -->
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400">
              Or access your school directly
            </span>
          </div>
        </div>

        <!-- School Access -->
        <div class="space-y-3">
          <div>
            <label for="schoolCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              School Code
            </label>
            <div class="mt-1 flex rounded-md shadow-sm">
              <input 
                id="schoolCode" 
                v-model="schoolCode" 
                type="text" 
                placeholder="e.g., xba1224"
                class="flex-1 min-w-0 block w-full px-3 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
              <button 
                type="button" 
                @click="goToSchool"
                :disabled="!schoolCode.trim()"
                class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-300 text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-600"
              >
                Go
              </button>
            </div>
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Enter your school code to access {{ schoolCode.trim() ? `${schoolCode.trim()}.rphmate.com` : 'your school portal' }}
            </p>
          </div>
        </div>
      </form>
    </div>
  </section>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  layout: 'landing'
})

// Form state
const form = ref({
  email: '',
  password: '',
  rememberMe: false
})

const showPassword = ref(false)
const isLoading = ref(false)
const errorMessage = ref('')
const schoolCode = ref('')

// Handle form submission
const handleSubmit = async () => {
  isLoading.value = true
  errorMessage.value = ''

  try {
    // TODO: Implement actual authentication
    console.log('Logging in:', form.value)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // For now, simulate successful login
    // In real implementation, this would validate credentials and redirect to school admin dashboard
    
    // Simulate finding user's school code and redirect
    const userSchoolCode = 'demo' // This would come from the API response
    await navigateTo(`/${userSchoolCode}`)
    
  } catch (error) {
    console.error('Login error:', error)
    errorMessage.value = 'Invalid email or password. Please try again.'
  } finally {
    isLoading.value = false
  }
}

// Go to school portal
const goToSchool = () => {
  if (schoolCode.value.trim()) {
    // In development, redirect to subdomain simulation
    if (process.env.NODE_ENV === 'development') {
      window.location.href = `http://${schoolCode.value.trim()}.localhost:3000`
    } else {
      window.location.href = `https://${schoolCode.value.trim()}.rphmate.com`
    }
  }
}

// Set page head
useHead({
  title: 'School Admin Sign In - RPHMate',
  meta: [
    {
      name: 'description',
      content: 'Sign in to your RPHMate school admin dashboard to manage your school, teachers, and billing.'
    }
  ]
})
</script>

<style scoped>
/* Custom styles for login page */
</style>
