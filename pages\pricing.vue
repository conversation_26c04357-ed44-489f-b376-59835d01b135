<template>
  <!-- Pricing Section -->
  <section class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-16">
        <h1 class="text-4xl sm:text-5xl font-bold text-gray-900 dark:text-white mb-4">
          Simple, Transparent Pricing
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
          Choose the perfect plan for your school. Start with a free trial and upgrade as you grow.
        </p>
        
        <!-- Billing Toggle -->
        <div class="flex items-center justify-center space-x-4">
          <span class="text-sm font-medium text-gray-900 dark:text-white" :class="{ 'text-gray-500': isYearly }">Monthly</span>
          <button @click="toggleBilling" class="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 dark:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <span class="sr-only">Toggle yearly billing</span>
            <span :class="isYearly ? 'translate-x-6' : 'translate-x-1'" class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"></span>
          </button>
          <span class="text-sm font-medium text-gray-900 dark:text-white" :class="{ 'text-gray-500': !isYearly }">
            Yearly 
            <span class="text-green-600 dark:text-green-400 font-semibold">(Save 20%)</span>
          </span>
        </div>
      </div>

      <!-- Pricing Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
        <!-- Basic Plan -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 border border-gray-200 dark:border-gray-700">
          <div class="text-center">
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Basic</h3>
            <div class="text-4xl font-bold text-gray-900 dark:text-white mb-2">
              RM{{ isYearly ? '79' : '99' }}
              <span class="text-lg font-normal text-gray-600 dark:text-gray-300">{{ isYearly ? '/month' : '/month' }}</span>
            </div>
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">
              {{ isYearly ? 'Billed annually (RM948/year)' : 'Billed monthly' }}
            </p>
          </div>
          
          <ul class="space-y-3 mb-8">
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">Up to 10 teachers</span>
            </li>
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">Unlimited lesson plans</span>
            </li>
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">Basic analytics</span>
            </li>
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">Email support</span>
            </li>
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">1-month free trial</span>
            </li>
          </ul>
          
          <NuxtLink to="/billing" class="w-full bg-gray-200 hover:bg-gray-300 text-gray-900 py-3 px-6 rounded-lg font-semibold transition-colors block text-center">
            Start Free Trial
          </NuxtLink>
        </div>

        <!-- Professional Plan (Most Popular) -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 border-2 border-blue-500 relative">
          <!-- Popular Badge -->
          <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <span class="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">Most Popular</span>
          </div>
          
          <div class="text-center">
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Professional</h3>
            <div class="text-4xl font-bold text-gray-900 dark:text-white mb-2">
              RM{{ isYearly ? '159' : '199' }}
              <span class="text-lg font-normal text-gray-600 dark:text-gray-300">{{ isYearly ? '/month' : '/month' }}</span>
            </div>
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">
              {{ isYearly ? 'Billed annually (RM1,908/year)' : 'Billed monthly' }}
            </p>
          </div>
          
          <ul class="space-y-3 mb-8">
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">Up to 50 teachers</span>
            </li>
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">Everything in Basic</span>
            </li>
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">Advanced analytics</span>
            </li>
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">Priority support</span>
            </li>
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">Custom templates</span>
            </li>
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">1-month free trial</span>
            </li>
          </ul>
          
          <NuxtLink to="/billing" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors block text-center">
            Start Free Trial
          </NuxtLink>
        </div>

        <!-- Enterprise Plan -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 border border-gray-200 dark:border-gray-700">
          <div class="text-center">
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Enterprise</h3>
            <div class="text-4xl font-bold text-gray-900 dark:text-white mb-2">
              RM{{ isYearly ? '319' : '399' }}
              <span class="text-lg font-normal text-gray-600 dark:text-gray-300">{{ isYearly ? '/month' : '/month' }}</span>
            </div>
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">
              {{ isYearly ? 'Billed annually (RM3,828/year)' : 'Billed monthly' }}
            </p>
          </div>
          
          <ul class="space-y-3 mb-8">
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">Unlimited teachers</span>
            </li>
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">Everything in Professional</span>
            </li>
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">Custom integrations</span>
            </li>
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">Dedicated support</span>
            </li>
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">Custom training</span>
            </li>
            <li class="flex items-center">
              <UiBaseIcon name="heroicons:check" class="h-5 w-5 text-green-500 mr-3" />
              <span class="text-gray-600 dark:text-gray-300">1-month free trial</span>
            </li>
          </ul>
          
          <NuxtLink to="/billing" class="w-full bg-gray-200 hover:bg-gray-300 text-gray-900 py-3 px-6 rounded-lg font-semibold transition-colors block text-center">
            Start Free Trial
          </NuxtLink>
        </div>
      </div>

      <!-- FAQ Section -->
      <div class="mt-20">
        <h2 class="text-3xl font-bold text-center text-gray-900 dark:text-white mb-12">
          Frequently Asked Questions
        </h2>
        
        <div class="max-w-3xl mx-auto space-y-6">
          <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              How does the free trial work?
            </h3>
            <p class="text-gray-600 dark:text-gray-300">
              All plans include a 1-month free trial with full access to features. No credit card required to start. 
              After the trial, you'll have a 7-day grace period before access is restricted.
            </p>
          </div>
          
          <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Can I change plans later?
            </h3>
            <p class="text-gray-600 dark:text-gray-300">
              Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, 
              and we'll prorate the billing accordingly.
            </p>
          </div>
          
          <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              What happens if I exceed the teacher limit?
            </h3>
            <p class="text-gray-600 dark:text-gray-300">
              You'll receive a notification when approaching your limit. You can either upgrade your plan 
              or remove inactive teachers to stay within your current limit.
            </p>
          </div>
          
          <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Is my data secure?
            </h3>
            <p class="text-gray-600 dark:text-gray-300">
              Absolutely. We use enterprise-grade security with encrypted data storage and transmission. 
              Your school's data is completely isolated from other schools.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  layout: 'landing'
})

// Billing toggle state
const isYearly = ref(false)

const toggleBilling = () => {
  isYearly.value = !isYearly.value
}

// Set page head
useHead({
  title: 'Pricing - RPHMate',
  meta: [
    {
      name: 'description',
      content: 'Simple, transparent pricing for RPHMate. Choose the perfect plan for your school with a 1-month free trial. Basic, Professional, and Enterprise plans available.'
    },
    {
      property: 'og:title',
      content: 'Pricing - RPHMate'
    },
    {
      property: 'og:description',
      content: 'Simple, transparent pricing for RPHMate educational platform. Start with a free trial.'
    }
  ]
})
</script>

<style scoped>
/* Custom styles for pricing page */
</style>
