#!/usr/bin/env node

/**
 * Test script to verify the authentication flow
 * Run with: node scripts/test-auth-flow.js
 */

const { execSync } = require('child_process')

console.log('🧪 Testing Authentication Flow...\n')

// Test 1: Check if the login page exists and has correct content
console.log('1. Testing login page...')
try {
  const loginContent = require('fs').readFileSync('pages/login.vue', 'utf8')
  
  if (loginContent.includes('handleSubmit') && loginContent.includes('supabase.auth.signInWithPassword')) {
    console.log('   ✅ Login page has proper authentication implementation')
  } else {
    console.log('   ❌ Login page missing authentication implementation')
  }
  
  if (loginContent.includes('/api/schools/user-schools')) {
    console.log('   ✅ Login page fetches user schools after authentication')
  } else {
    console.log('   ❌ Login page missing user schools fetch')
  }
} catch (error) {
  console.log('   ❌ Error reading login page:', error.message)
}

// Test 2: Check if the user-schools API exists
console.log('\n2. Testing user-schools API...')
try {
  const apiContent = require('fs').readFileSync('server/api/schools/user-schools.get.ts', 'utf8')
  
  if (apiContent.includes('supabase.auth.getUser')) {
    console.log('   ✅ API validates user authentication')
  } else {
    console.log('   ❌ API missing user authentication validation')
  }
  
  if (apiContent.includes('development fallback')) {
    console.log('   ✅ API has development fallback for testing')
  } else {
    console.log('   ❌ API missing development fallback')
  }
} catch (error) {
  console.log('   ❌ Error reading user-schools API:', error.message)
}

// Test 3: Check if school admin dashboard has middleware
console.log('\n3. Testing school admin dashboard...')
try {
  const dashboardContent = require('fs').readFileSync('pages/[schoolcode].vue', 'utf8')
  
  if (dashboardContent.includes('school-admin-auth')) {
    console.log('   ✅ Dashboard has school admin authentication middleware')
  } else {
    console.log('   ❌ Dashboard missing authentication middleware')
  }
  
  if (dashboardContent.includes('fetchSchoolData')) {
    console.log('   ✅ Dashboard fetches real school data')
  } else {
    console.log('   ❌ Dashboard using only mock data')
  }
} catch (error) {
  console.log('   ❌ Error reading dashboard page:', error.message)
}

// Test 4: Check if middleware exists
console.log('\n4. Testing middleware...')
try {
  const middlewareContent = require('fs').readFileSync('middleware/school-admin-auth.ts', 'utf8')
  
  if (middlewareContent.includes('validate-admin-access')) {
    console.log('   ✅ Middleware validates school admin access')
  } else {
    console.log('   ❌ Middleware missing access validation')
  }
} catch (error) {
  console.log('   ❌ Error reading middleware:', error.message)
}

// Test 5: Check if landing page links to correct login
console.log('\n5. Testing landing page...')
try {
  const landingContent = require('fs').readFileSync('pages/index.vue', 'utf8')
  
  if (landingContent.includes('to="/login"')) {
    console.log('   ✅ Landing page links to correct login page')
  } else {
    console.log('   ❌ Landing page has incorrect login link')
  }
} catch (error) {
  console.log('   ❌ Error reading landing page:', error.message)
}

console.log('\n🎯 Authentication Flow Test Summary:')
console.log('   The authentication flow has been updated to:')
console.log('   1. Landing page "Sign In" → /login (school admin login)')
console.log('   2. School admin login → authenticate with Supabase')
console.log('   3. Fetch user schools → redirect to /{schoolcode}')
console.log('   4. School admin dashboard → protected by middleware')
console.log('   5. Middleware validates school admin access')
console.log('\n✨ Ready for testing!')
