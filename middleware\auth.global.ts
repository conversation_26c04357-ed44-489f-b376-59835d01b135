import { navigateTo, defineNuxtRouteMiddleware } from "#app";
import { useSupabase } from "~/composables/useSupabase";

export default defineNuxtRouteMiddleware(async (to, from) => {
  // Enable auth middleware for Phase 2 implementation
  // Updated for new RPHMate SaaS architecture

  // Skip auth middleware on server-side for static assets and API routes
  if (process.server && (to.path.startsWith('/_') || to.path.startsWith('/api/'))) {
    return;
  }

  const { client } = useSupabase();
  const {
    data: { session },
  } = await client.auth.getSession();

  // Get current subdomain context - but handle the case where it might not be set yet
  const currentSubdomain = useState('currentSubdomain').value;
  const isSchoolContext = !!currentSubdomain && currentSubdomain !== 'auth';

  // Check if we're on the main domain (localhost:3000 without subdomain)
  const isMainDomain = process.client ?
    (window.location.hostname === 'localhost' || !window.location.hostname.includes('.')) :
    !currentSubdomain;

  const publicPaths = [
    "/",
    "/pricing",
    "/billing",
    "/login",
    "/success",
    "/auth/login",
    "/auth/daftar",
    "/auth/confirm",
    "/auth/butir-asas",
  ];

  // Add school-specific auth paths only for valid school subdomains
  if (isSchoolContext) {
    publicPaths.push(
      `/${currentSubdomain}/auth/login`,
      `/${currentSubdomain}/auth/daftar`,
      `/${currentSubdomain}/auth/confirm`,
      `/${currentSubdomain}/auth/butir-asas`
    );
  }

  // If there is no session and the user is trying to access a protected page,
  // redirect them to the appropriate login page.
  if (!session && !publicPaths.includes(to.path)) {
    if (isSchoolContext) {
      return navigateTo(`/${currentSubdomain}/auth/login`);
    } else if (isMainDomain) {
      // For main domain, redirect to main login page
      return navigateTo("/login");
    } else {
      // Fallback to main login
      return navigateTo("/login");
    }
  }

  // If there IS a session and the user is trying to access a public auth page,
  // redirect them to the appropriate homepage.
  if (session && (to.path === "/auth/login" || to.path === "/auth/daftar" ||
                  to.path === `/${currentSubdomain}/auth/login` ||
                  to.path === `/${currentSubdomain}/auth/daftar`)) {
    if (isSchoolContext) {
      return navigateTo(`/${currentSubdomain}`);
    } else {
      return navigateTo("/");
    }
  }

  // Otherwise, allow navigation to proceed.
});
