// Get user schools API endpoint
// Created: 2025-07-13

import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    // Get authorization header
    const authHeader = getHeader(event, 'authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing or invalid authorization header'
      })
    }

    const token = authHeader.replace('Bearer ', '')

    // Initialize Supabase client with the user's token
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: authHeader
          }
        }
      }
    )

    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired token'
      })
    }

    // Get user's school memberships with school data
    const { data: memberships, error: membershipError } = await supabase
      .from('school_memberships')
      .select(`
        *,
        school:schools(*)
      `)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .order('joined_at', { ascending: false })

    if (membershipError) {
      console.error('Error fetching user schools:', membershipError)
      throw createError({
        statusCode: 500,
        statusMessage: 'Error fetching user schools'
      })
    }

    // Transform the data
    const schools = memberships?.map(membership => ({
      ...membership.school,
      membership: {
        id: membership.id,
        role: membership.role,
        status: membership.status,
        joined_at: membership.joined_at,
        permissions: membership.permissions
      }
    })) || []

    // Get admin schools (schools where user is the admin_user_id)
    const { data: adminSchools, error: adminError } = await supabase
      .from('schools')
      .select('*')
      .eq('admin_user_id', user.id)

    if (adminError) {
      console.error('Error fetching admin schools:', adminError)
      // Don't fail the request, just log the error
    }

    // Add admin schools that might not have memberships
    if (adminSchools) {
      for (const adminSchool of adminSchools) {
        const existingSchool = schools.find(s => s.id === adminSchool.id)
        if (!existingSchool) {
          schools.push({
            ...adminSchool,
            membership: {
              id: null,
              role: 'admin',
              status: 'active',
              joined_at: adminSchool.created_at,
              permissions: {}
            }
          })
        }
      }
    }

    return {
      success: true,
      schools,
      total: schools.length
    }

  } catch (error: any) {
    console.error('User schools API error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
