<template>
  <section class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
      <!-- Success Icon -->
      <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-green-100 dark:bg-green-900/30">
        <svg class="h-12 w-12 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
      </div>

      <!-- Header -->
      <div>
        <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white">
          Welcome to RPHMate!
        </h2>
        <p class="mt-2 text-lg text-gray-600 dark:text-gray-300">
          Your school account has been created successfully
        </p>
      </div>

      <!-- School Info -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          School Details
        </h3>
        <div class="space-y-2 text-left">
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-300">School Name:</span>
            <span class="font-medium text-gray-900 dark:text-white">Your School</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-300">School Code:</span>
            <span class="font-medium text-gray-900 dark:text-white">{{ schoolCode }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-300">Plan:</span>
            <span class="font-medium text-gray-900 dark:text-white">Professional</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600 dark:text-gray-300">Status:</span>
            <span class="font-medium text-green-600 dark:text-green-400">30 days free trial</span>
          </div>
        </div>
      </div>

      <!-- School URL -->
      <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <h4 class="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2">
          Your School Portal
        </h4>
        <div class="flex items-center space-x-2">
          <code class="flex-1 text-sm bg-white dark:bg-gray-800 px-3 py-2 rounded border text-blue-600 dark:text-blue-400">
            {{ schoolUrl }}
          </code>
        </div>
        <p class="text-xs text-blue-700 dark:text-blue-300 mt-2">
          Share this URL with your teachers to give them access to your school
        </p>
      </div>

      <!-- Next Steps -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Next Steps
        </h4>
        <div class="space-y-3 text-left">
          <div class="flex items-start space-x-3">
            <div class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
              1
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">Access your admin dashboard</p>
              <p class="text-xs text-gray-600 dark:text-gray-300">Manage billing and school settings</p>
            </div>
          </div>
          <div class="flex items-start space-x-3">
            <div class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
              2
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">Invite your teachers</p>
              <p class="text-xs text-gray-600 dark:text-gray-300">Share the school portal URL with your team</p>
            </div>
          </div>
          <div class="flex items-start space-x-3">
            <div class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
              3
            </div>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">Start creating lesson plans</p>
              <p class="text-xs text-gray-600 dark:text-gray-300">Begin using RPHMate's educational tools</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="space-y-3">
        <a 
          href="http://localhost:3000"
          class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors block text-center"
        >
          Go to Main Dashboard
        </a>
        
        <a
          :href="schoolUrl"
          target="_blank"
          class="w-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 py-3 px-6 rounded-lg font-semibold transition-colors block text-center"
        >
          Visit School Portal
        </a>
      </div>
    </div>
  </section>


</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  layout: 'landing'
})

// Get session ID from URL
const route = useRoute()
const sessionId = route.query.session_id as string || 'unknown'

// Generate school code from session ID
const schoolCode = computed(() => {
  return 'school-' + sessionId.slice(-8)
})

// Generate school URL
const schoolUrl = computed(() => {
  if (process.env.NODE_ENV === 'development') {
    return `http://${schoolCode.value}.localhost:3000`
  }
  return `https://${schoolCode.value}.rphmate.com`
})

// Log for debugging
onMounted(() => {
  console.log('✅ Success page loaded with school code:', schoolCode.value)
  console.log('🌐 School URL:', schoolUrl.value)
})

// Set page head
useHead({
  title: 'Welcome to RPHMate - Account Created Successfully',
  meta: [
    {
      name: 'description',
      content: 'Your RPHMate school account has been created successfully. Start your educational journey today.'
    }
  ]
})
</script>

<style scoped>
/* Custom styles for success page */
</style>
