import { useSupabaseUser, useSupabase<PERSON>lient, navigateTo } from "#imports";

interface Profile {
  is_profile_complete: boolean;
}

export default defineNuxtRouteMiddleware(async (to) => {
  const user = useSupabaseUser();
  const client = useSupabaseClient();

  // Allow navigation to login, confirm, daftar, and butir-asas pages even if profile is incomplete or user is not logged in
  const allowedPaths = [
    "/auth/login",
    "/auth/daftar",
    "/auth/confirm",
    "/auth/butir-asas",
  ];
  if (allowedPaths.includes(to.path)) {
    return;
  }

  // If user is not logged in, auth.global.ts should handle redirection to login.
  // This middleware only cares about logged-in users with incomplete profiles.
  if (!user.value) {
    return; // auth.global.ts will redirect to /auth/login
  }

  try {
    const { data, error } = await client
      .from("profiles")
      .select("is_profile_complete")
      .eq("id", user.value.id)
      .single<Profile>(); // Specify the type here

    if (error) {
      console.error("Error fetching profile status:", error);
      return;
    }

    if (data && !data.is_profile_complete) {
      return navigateTo("/auth/butir-asas");
    }
  } catch (e) {
    console.error("Exception while checking profile status:", e);
    // Allow access by default if there's an unexpected error, or redirect to login/error page
  }
});
