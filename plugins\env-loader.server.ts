// Server-side plugin to ensure environment variables are loaded
import dotenv from 'dotenv'
import { existsSync } from 'fs'

export default defineNuxtPlugin(() => {
  // Load environment variables from .env.local if it exists
  if (existsSync('.env.local')) {
    dotenv.config({ path: '.env.local' })
    console.log('🔧 Loaded environment variables from .env.local')
  }
  
  // Also try .env
  if (existsSync('.env')) {
    dotenv.config({ path: '.env' })
    console.log('🔧 Loaded environment variables from .env')
  }
  
  // Debug: Check if Stripe keys are now available
  console.log('🔍 Environment check:', {
    STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY ? 'Present' : 'Missing',
    STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET ? 'Present' : 'Missing',
    SUPABASE_URL: process.env.SUPABASE_URL ? 'Present' : 'Missing'
  })
})
