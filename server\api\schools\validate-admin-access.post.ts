// API endpoint to validate school admin access
// Created for Phase 2: Subdomain Infrastructure

import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    // Get authorization header
    const authHeader = getHeader(event, 'authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Authorization header required'
      })
    }

    // Get request body
    const body = await readBody(event)
    const { schoolCode } = body

    if (!schoolCode) {
      throw createError({
        statusCode: 400,
        statusMessage: 'School code is required'
      })
    }

    // Extract token
    const token = authHeader.substring(7)
    
    // Get Supabase client
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: authHeader
          }
        }
      }
    )
    
    // Verify the token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid token'
      })
    }

    // Check if user has admin access to this school
    // For now, we'll implement a simple check
    // In a real implementation, you'd check against a schools table
    
    // TODO: Replace with actual database query
    // Example query:
    // const { data: schoolAccess } = await supabase
    //   .from('school_admins')
    //   .select('*')
    //   .eq('user_id', user.id)
    //   .eq('school_code', schoolCode)
    //   .eq('role', 'admin')
    //   .single()

    // For development, allow access to sample schools
    const sampleSchools = ['xba1224', 'demo', 'test', 'school1', 'school2']
    const hasAccess = sampleSchools.includes(schoolCode.toLowerCase())

    return {
      success: true,
      hasAccess,
      schoolCode,
      userId: user.id,
      message: hasAccess ? 'Access granted' : 'Access denied'
    }

  } catch (error: any) {
    console.error('Error validating school admin access:', error)
    
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Internal server error'
    })
  }
})
