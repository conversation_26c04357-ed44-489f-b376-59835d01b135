<template>
    <div
        class="fixed inset-0 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-2 sm:p-4 md:p-6 overflow-auto">
        <Card variant="shadow-2xl"
            class="w-full max-w-sm sm:max-w-md md:max-w-2xl lg:max-w-4xl overflow-hidden shadow-2xl" no-default-spacing>
            <div id="butir-asas-panel" class="space-y-8 p-4 sm:p-6 lg:p-8">
                <div>
                    <h2
                        class="mt-6 text-center text-3xl font-extrabold text-light-foreground dark:text-dark-foreground">
                        Butira<PERSON>
                    </h2>
                    <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
                        Sila isikan maklumat berikut untuk melengkapkan pendaftaran <span v-if="userEmail">({{ userEmail
                        }})</span>.
                    </p>
                </div>
                <form class="mt-8 space-y-6 sm:space-y-8" @submit.prevent="submitForm" novalidate>
                    <fieldset class="border border-gray-300 dark:border-gray-700 p-4 sm:p-6 rounded-md">
                        <legend class="text-lg font-medium text-gray-900 dark:text-white px-2">Maklumat Asas</legend>
                        <!-- Two-Column Layout for Profile Picture and Main Details -->
                        <div class="flex flex-col md:flex-row gap-6 md:items-center">
                            <!-- Left Column: Profile Upload -->
                            <div class="md:w-1/3 flex flex-col items-center">
                                <ProfileUpload v-model="formData.avatar_file" @error="handleAvatarClientError" />
                                <span v-if="formErrors?.avatar_url" id="avatar_url-error"
                                    class="text-alert-error text-sm mt-1">
                                    {{ formErrors.avatar_url.join(', ') }}
                                </span>
                            </div>

                            <!-- Right Column: Inputs -->
                            <div class="md:w-2/3 space-y-6">
                                <div>
                                    <!-- External label removed, relying on internal FloatingLabel of Input.vue -->
                                    <Input id="nama-penuh" v-model="formData.full_name" type="text"
                                        placeholder="Nama Penuh" @blur="validateField('full_name')"
                                        :aria-invalid="!!formErrors?.full_name" aria-describedby="full_name-error" />
                                    <span v-if="formErrors?.full_name" id="full_name-error"
                                        class="text-alert-error text-sm mt-1">
                                        {{ formErrors.full_name.join(', ') }}
                                    </span>
                                </div>
                                <div>
                                    <!-- External label removed, relying on internal FloatingLabel of SingleSelect.vue -->
                                    <SingleSelect id="jantina" v-model="formData.jantina" :options="jantinaOptions"
                                        placeholder="Jantina" boundary-selector="#butir-asas-panel"
                                        @update:modelValue="validateField('jantina')"
                                        :aria-invalid="!!formErrors?.jantina" aria-describedby="jantina-error" />
                                    <span v-if="formErrors?.jantina" id="jantina-error"
                                        class="text-alert-error text-sm mt-1">
                                        {{ formErrors.jantina.join(', ') }}
                                    </span>
                                </div>
                                <div>
                                    <!-- External label removed, relying on internal FloatingLabel of SingleSelect.vue -->
                                    <SingleSelect id="peranan" v-model="formData.peranan"
                                        :options="perananOptionsForSelect" placeholder="Peranan"
                                        boundary-selector="#butir-asas-panel"
                                        @update:modelValue="validateField('peranan')"
                                        :aria-invalid="!!formErrors?.peranan" aria-describedby="peranan-error">
                                        <template #customOptionLabel="{ option, active, selected }">
                                            <div class="flex items-center">
                                                <span
                                                    :class="['block truncate', selected ? 'font-semibold' : 'font-normal']">
                                                    {{ option.label }}
                                                </span>
                                                <div v-if="option.isUserAdded" class="flex items-center space-x-1 ml-2">
                                                    <p>-</p>
                                                    <Button type="button" size="xs" variant="flat"
                                                        @click.stop="editPeranan(option as any)"
                                                        class="p-1 rounded hover:bg-primary hover:text-white"
                                                        :title="`Edit ${option.label}`">Edit
                                                    </Button>
                                                    <p>|</p>
                                                    <Button type="button" size="xs" variant="flat"
                                                        @click.stop="deletePeranan(option as any)"
                                                        class="p-1 rounded hover:bg-primary hover:text-white"
                                                        :title="`Padam ${option.label}`">Padam
                                                    </Button>
                                                </div>
                                            </div>
                                        </template>
                                    </SingleSelect>
                                    <span v-if="formErrors?.peranan" id="peranan-error"
                                        class="text-alert-error text-sm mt-1">
                                        {{ formErrors.peranan.join(', ') }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </fieldset>

                    <!-- Subjek Kelas - Full Width Below, wrapped in a fieldset -->
                    <fieldset class="border border-gray-300 dark:border-gray-700 p-4 sm:p-6 rounded-md min-w-0">
                        <legend class="text-lg font-medium text-gray-900 dark:text-white px-2">Subjek Kelas</legend>
                        <ClassSubject class="mt-1" @update:data="handleClassSubjectUpdate"
                            :initial-data="formData.classSubjectsData" />
                        <!-- Error display for classSubjectsData -->
                        <span v-if="formErrors?.classSubjectsData" id="classSubjectsData-error"
                            class="text-alert-error text-sm mt-1">
                            {{ formErrors.classSubjectsData.join(', ') }}
                        </span>
                    </fieldset>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row-reverse gap-4 mt-6">
                        <Button type="submit" variant="primary" size="lg" class="w-full" :loading="loading"
                            :disabled="loading">
                            {{ loading ? 'Menghantar...' : 'Hantar' }}
                        </Button>
                        <Button type="button" variant="outline" size="lg" class="w-full" @click="handleCancel"
                            :disabled="loading || cancelling">
                            {{ cancelling ? 'Membatalkan...' : 'Batal' }}
                        </Button>
                    </div>

                    <!-- General Error Message (already exists, but now primarily for non-field specific errors or Zod summary) -->
                    <div v-if="errorMsg"
                        class="mt-4 p-3 text-sm text-alert-error-text bg-alert-error rounded-md dark:bg-alert-error dark:text-alert-error-text"
                        role="alert">
                        {{ errorMsg }}
                    </div>
                    <div v-if="successMsg"
                        class="mt-4 p-3 text-sm text-green-700 bg-green-100 rounded-md dark:bg-green-700 dark:text-green-100"
                        role="alert">
                        {{ successMsg }}
                    </div>
                </form>
            </div>
        </Card>
        <Modal :is-open="isAddPerananModalOpen" title="Tambah Peranan Baru"
            @update:is-open="isAddPerananModalOpen = $event">
            <div class="p-4">
                <form @submit.prevent="handleAddNewPeranan" class="space-y-4">
                    <div>
                        <Input id="new-peranan-name" v-model="newPerananName" type="text" placeholder="Nama Peranan" />
                    </div>
                    <div class="flex flex-col-reverse gap-y-2 sm:flex-row sm:justify-end sm:gap-y-0 sm:space-x-3 mt-4">
                        <Button type="button" @click="isAddPerananModalOpen = false" variant="outline">
                            Batal
                        </Button>
                        <Button type="submit" variant="primary" :disabled="!newPerananName.trim()">
                            Simpan
                        </Button>
                    </div>
                </form>
            </div>
        </Modal>

        <!-- Edit Peranan Modal -->
        <Modal :is-open="isEditPerananModalOpen" title="Edit Peranan" @update:is-open="isEditPerananModalOpen = $event">
            <div class="p-4">
                <form @submit.prevent="handleEditPeranan" class="space-y-4">
                    <div>
                        <Input id="edit-peranan-name" v-model="editPerananName" type="text"
                            placeholder="Nama Peranan" />
                    </div>
                    <div class="flex flex-col-reverse gap-y-2 sm:flex-row sm:justify-end sm:gap-y-0 sm:space-x-3 mt-4">
                        <Button type="button" @click="isEditPerananModalOpen = false" variant="outline">
                            Batal
                        </Button>
                        <Button type="submit" variant="primary" :disabled="!editPerananName.trim()">
                            Kemaskini
                        </Button>
                    </div>
                </form>
            </div>
        </Modal>

        <!-- Delete Peranan Confirmation Modal -->
        <DeleteConfirmationModal :is-open="isDeletePerananModalOpen" title="Padam Peranan" item-type="peranan"
            :item-name="deletingPeranan?.label || ''" danger-level="medium" @confirm="handleDeletePeranan"
            @cancel="isDeletePerananModalOpen = false" @update:is-open="isDeletePerananModalOpen = $event" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { useRouter, useSupabaseUser } from '#imports';
import { useSupabase } from '~/composables/useSupabase';
import Input from '~/components/ui/base/Input.vue';
import SingleSelect from '~/components/ui/base/SingleSelect.vue';
import ClassSubject from '~/components/ui/composite/ClassSubject.vue';
import ProfileUpload from '~/components/ui/composite/ProfileUpload.vue';
import Button from '~/components/ui/base/Button.vue';
import Card from '~/components/ui/composite/Card.vue';
import Modal from '~/components/ui/composite/Modal.vue';
import Icon from '~/components/ui/base/Icon.vue';
import DeleteConfirmationModal from '~/components/ui/composite/DeleteConfirmationModal.vue';
import { ProfileCompletionSchema, type ProfileCompletionForm } from '~/schemas';

definePageMeta({
    layout: 'blank',
});

const formData = ref<ProfileCompletionForm & { avatar_file?: File | null }>({
    full_name: '',
    jantina: '' as string, // Initialize with an empty string or a valid default from options
    peranan: '' as string, // Initialize with an empty string or a valid default from options
    avatar_url: null,
    avatar_file: null, // New field for the File object
    classSubjectsData: [],
});

const formErrors = ref<Record<keyof ProfileCompletionForm, string[] | undefined> | null>(null);

const loading = ref(false);
const errorMsg = ref<string | null>(null);
const successMsg = ref<string | null>(null);
const cancelling = ref(false);

const user = useSupabaseUser();
const userEmail = computed(() => user.value?.email || null);

const { client } = useSupabase();
const router = useRouter();

// Removed handleAvatarUpload as v-model handles file binding

const handleAvatarClientError = (errorPayload: { type: string, message: string, file?: File }) => {
    errorMsg.value = `Ralat fail avatar: ${errorPayload.message}`;
    if (!formErrors.value) formErrors.value = {} as Record<keyof ProfileCompletionForm, string[] | undefined>;
    formErrors.value.avatar_url = [errorPayload.message];
    formData.value.avatar_file = null;
    formData.value.avatar_url = null; // Clear any potentially stale URL
};

const handleClassSubjectUpdate = (data: ProfileCompletionForm['classSubjectsData']) => {
    formData.value.classSubjectsData = data;
    validateField('classSubjectsData'); // Validate the field as usual

    // If the update resulted in an empty list (e.g., last item deleted),
    // clear the specific validation message for classSubjectsData.
    // The main form validation on submit will still enforce the .min(1) rule.
    if (data.length === 0 && formErrors.value?.classSubjectsData) {
        formErrors.value.classSubjectsData = undefined;
    }
};

const handleCancel = async () => {
    cancelling.value = true;
    errorMsg.value = null;
    successMsg.value = null;
    try {
        const { error: signOutError } = await client.auth.signOut();
        if (signOutError) {
            console.error('Error signing out:', signOutError);
            errorMsg.value = 'Amaran: Gagal log keluar sepenuhnya pada pelayan, tetapi akan cuba log keluar setempat. (' + signOutError.message + ')';
        }
        router.push('/auth/login');
    } catch (e: any) {
        errorMsg.value = e.message || 'Berlaku ralat semasa pembatalan.';
        cancelling.value = false;
    }
    if (errorMsg.value) {
        cancelling.value = false;
    }
};

const jantinaOptions = ref([
    { value: 'lelaki', label: 'Lelaki' },
    { value: 'perempuan', label: 'Perempuan' },
]);

const perananOptions = ref([
    { value: 'gb', label: 'Guru Besar / Pengetua', isUserAdded: false },
    { value: 'gpk', label: 'Guru Penolong Kanan', isUserAdded: false },
    { value: 'gp', label: 'Guru Penyelaras', isUserAdded: false },
    { value: 'gc', label: 'Guru Cemerlang', isUserAdded: false },
    { value: 'ga', label: 'Guru Akademik', isUserAdded: false },
]);

const ADD_NEW_PERANAN_VALUE = 'add_new_peranan';
const isAddPerananModalOpen = ref(false);
const newPerananName = ref('');

const perananOptionsForSelect = computed(() => {
    const mappedOptions = perananOptions.value.map(option => ({
        ...option,
        labelSlot: option.isUserAdded
    }));

    // Only show "Tambah Peranan" option if no user-added peranan exists
    const hasUserAddedPeranan = perananOptions.value.some(option => option.isUserAdded);

    if (hasUserAddedPeranan) {
        return mappedOptions;
    } else {
        return [
            ...mappedOptions,
            { value: ADD_NEW_PERANAN_VALUE, label: '+ Tambah Peranan', isUserAdded: false, labelSlot: false }
        ];
    }
});

watch(() => formData.value.peranan, (newValue, oldValue) => {
    if (newValue === ADD_NEW_PERANAN_VALUE) {
        isAddPerananModalOpen.value = true;
        // Use nextTick to ensure the UI has a chance to update before we change the model value back
        nextTick(() => {
            formData.value.peranan = oldValue || ''; // Revert to old value or empty
        });
    }
});

// State for edit modal
const isEditPerananModalOpen = ref(false);
const editingPeranan = ref<{ value: string; label: string; isUserAdded: boolean } | null>(null);
const editPerananName = ref('');

// State for delete confirmation
const isDeletePerananModalOpen = ref(false);
const deletingPeranan = ref<{ value: string; label: string; isUserAdded: boolean } | null>(null);

const handleAddNewPeranan = () => {
    const name = newPerananName.value.trim();
    if (name) {
        // Basic check for duplicates
        const isDuplicate = perananOptions.value.some(opt => opt.label.toLowerCase() === name.toLowerCase());
        if (isDuplicate) {
            // Maybe show an error to the user
            console.warn("Peranan already exists.");
            return;
        }
        // Create a simple value from the name, e.g., "Guru Kanan" -> "guru_kanan"
        const value = name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');

        perananOptions.value.push({ value, label: name, isUserAdded: true });

        // Select the new option
        formData.value.peranan = value;

        // Close modal and reset
        isAddPerananModalOpen.value = false;
        newPerananName.value = '';
    }
};

const editPeranan = (option: { value: string; label: string; isUserAdded: boolean }) => {
    editingPeranan.value = option;
    editPerananName.value = option.label;
    isEditPerananModalOpen.value = true;
};

const handleEditPeranan = () => {
    const name = editPerananName.value.trim();
    if (name && editingPeranan.value) {
        // Basic check for duplicates (excluding the current option)
        const isDuplicate = perananOptions.value.some(opt =>
            opt.value !== editingPeranan.value!.value &&
            opt.label.toLowerCase() === name.toLowerCase()
        );
        if (isDuplicate) {
            console.warn("Peranan already exists.");
            return;
        }

        // Find and update the option
        const optionIndex = perananOptions.value.findIndex(opt => opt.value === editingPeranan.value!.value);
        if (optionIndex !== -1) {
            perananOptions.value[optionIndex].label = name;
        }

        // Close modal and reset
        isEditPerananModalOpen.value = false;
        editingPeranan.value = null;
        editPerananName.value = '';
    }
};

const deletePeranan = (option: { value: string; label: string; isUserAdded: boolean }) => {
    deletingPeranan.value = option;
    isDeletePerananModalOpen.value = true;
};

const handleDeletePeranan = () => {
    if (deletingPeranan.value) {
        // Remove the option from the list
        const optionIndex = perananOptions.value.findIndex(opt => opt.value === deletingPeranan.value!.value);
        if (optionIndex !== -1) {
            perananOptions.value.splice(optionIndex, 1);
        }

        // If the deleted option was selected, clear the selection
        if (formData.value.peranan === deletingPeranan.value.value) {
            formData.value.peranan = '';
        }

        // Close modal and reset
        isDeletePerananModalOpen.value = false;
        deletingPeranan.value = null;
    }
};

const validateForm = (): boolean => {
    const result = ProfileCompletionSchema.safeParse(formData.value);
    if (!result.success) {
        formErrors.value = result.error.flatten().fieldErrors as Record<keyof ProfileCompletionForm, string[] | undefined>;
        return false;
    }
    formErrors.value = null;
    errorMsg.value = null;
    return true;
};

const validateField = (fieldName: keyof ProfileCompletionForm) => {
    // Ensure the fieldName is a key of the schema shape
    if (!(fieldName in ProfileCompletionSchema.shape)) {
        console.warn(`Attempted to validate a field not in schema: ${fieldName}`);
        return;
    }

    // Correctly type the argument for pick
    const fieldToPick = { [fieldName]: true } as { [K in typeof fieldName]: true };
    const fieldSchema = ProfileCompletionSchema.pick(fieldToPick);

    // Explicitly handle null/undefined for jantina and peranan from SingleSelect
    let valueToValidate = formData.value[fieldName];
    if ((fieldName === 'jantina' || fieldName === 'peranan') && (valueToValidate === null || valueToValidate === undefined)) {
        valueToValidate = '';
    }

    const result = fieldSchema.safeParse({ [fieldName]: valueToValidate });

    if (!result.success) {
        if (!formErrors.value) formErrors.value = {} as Record<keyof ProfileCompletionForm, string[] | undefined>;
        // @ts-ignore - Zod's error structure can be complex, this assignment is generally safe here
        formErrors.value[fieldName] = result.error.flatten().fieldErrors[fieldName];
    } else {
        if (formErrors.value) {
            formErrors.value[fieldName] = undefined;
            if (Object.values(formErrors.value).every(err => !err || err.length === 0)) {
                errorMsg.value = null;
            }
        }
    }
};

const submitForm = async () => {
    errorMsg.value = null;
    successMsg.value = null;

    // Initial validation pass - avatar_url might be null here if a new file is selected
    // but not yet uploaded. This is okay, we re-validate after upload.
    if (!validateForm() && !formData.value.avatar_file) {
        // If no avatar file is pending upload and form is invalid, return.
        return;
    }

    loading.value = true;

    if (!user.value) {
        errorMsg.value = 'Tiada sesi pengguna aktif. Sila log masuk semula.';
        loading.value = false;
        return;
    }

    let finalAvatarUrl: string | null = formData.value.avatar_url ?? null; // Assume existing URL initially, coalesce undefined to null

    if (formData.value.avatar_file) {
        const file = formData.value.avatar_file;
        try {
            const fileExt = file.name.split('.').pop();
            const fileName = `${user.value.id}-${Date.now()}.${fileExt}`;
            const bucketName = 'avatars';
            const filePath = `${user.value.id}/${fileName}`;

            const { error: uploadError } = await client.storage
                .from(bucketName)
                .upload(filePath, file, {
                    cacheControl: '3600',
                    upsert: true,
                    contentType: file.type,
                });

            if (uploadError) {
                throw uploadError;
            }

            const { data: publicUrlData } = client.storage
                .from(bucketName)
                .getPublicUrl(filePath);

            if (!publicUrlData || !publicUrlData.publicUrl) {
                throw new Error('Tidak dapat memperolehi URL awam untuk avatar.');
            }
            finalAvatarUrl = publicUrlData.publicUrl;
            formData.value.avatar_url = finalAvatarUrl; // Update formData for final validation

        } catch (e: any) {
            console.error('Error uploading avatar:', e);
            errorMsg.value = `Ralat muat naik avatar: ${e.message}`;
            // Also update formErrors for avatar_url to show the error near the field
            if (!formErrors.value) formErrors.value = {} as Record<keyof ProfileCompletionForm, string[] | undefined>;
            formErrors.value.avatar_url = [e.message];
            loading.value = false;
            return;
        }
    }

    // Re-validate the entire form data, now with the potentially updated avatar_url
    if (!validateForm()) {
        loading.value = false;
        // errorMsg might already be set by validateForm if other fields are invalid.
        // If not, and avatar_url has an error, ensure it's displayed.
        if (!errorMsg.value && formErrors.value?.avatar_url && formErrors.value.avatar_url.length > 0) {
            errorMsg.value = "Sila semak ralat pada borang, termasuk bahagian avatar.";
        }
        return;
    }

    try {
        const dbUpdateData: any = {
            id: user.value.id,
            full_name: formData.value.full_name,
            gender: formData.value.jantina,      // Assuming DB column is 'gender'
            role: (() => {
                const selectedPeranan = perananOptions.value.find(
                    (option) => option.value === formData.value.peranan
                );
                if (selectedPeranan) {
                    return { code: selectedPeranan.value, label: selectedPeranan.label };
                }
                return formData.value.peranan; // Fallback if not found (e.g., empty string)
            })(),
            avatar_url: finalAvatarUrl, // Use the final URL (either newly uploaded or existing)
            is_profile_complete: true,
            class_subjects: formData.value.classSubjectsData,
        };

        const { error } = await client
            .from('profiles')
            .update(dbUpdateData)
            .eq('id', user.value.id);

        if (error) {
            console.error('Error updating profile:', error);
            errorMsg.value = `Pendaftaran gagal: ${error.message}`;
        } else {
            successMsg.value = 'Pendaftaran berjaya. Sila tunggu sementara kami mengalihkan anda ke halaman utama.';
            setTimeout(() => {
                router.push('/');
            }, 2000);
        }
    } catch (e: any) {
        console.error('Unexpected error during form submission:', e);
        errorMsg.value = e.message || 'Berlaku ralat tidak dijangka.';
    } finally {
        loading.value = false;
    }
};
</script>

<style scoped>
/* Add any page-specific styles here if needed */
</style>
