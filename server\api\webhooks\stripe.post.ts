// Stripe webhook handler for payment processing
// Phase 3: Payment Integration

import Stripe from 'stripe'

// Initialize Stripe - will be done inside the handler to access runtime config

export default defineEventHandler(async (event) => {
  try {
    // Get Stripe keys from environment variables
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY
    const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET

    if (!stripeSecretKey || !stripeWebhookSecret) {
      console.error('Stripe environment variables missing:', {
        secretKey: stripeSecretKey ? 'Present' : 'Missing',
        webhookSecret: stripeWebhookSecret ? 'Present' : 'Missing'
      })
      throw createError({
        statusCode: 500,
        statusMessage: 'Stripe configuration missing'
      })
    }

    const stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2025-06-30.basil'
    })

    const body = await readRawBody(event)
    const signature = getHeader(event, 'stripe-signature')

    if (!signature || !body) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing signature or body'
      })
    }

    // Verify webhook signature
    let stripeEvent: Stripe.Event
    try {
      stripeEvent = stripe.webhooks.constructEvent(
        body,
        signature,
        stripeWebhookSecret
      )
    } catch (err: any) {
      console.error('Webhook signature verification failed:', err.message)
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid signature'
      })
    }

    console.log('Received Stripe webhook:', stripeEvent.type)

    // Handle different event types
    switch (stripeEvent.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(stripeEvent.data.object as Stripe.Checkout.Session)
        break
        
      case 'customer.subscription.created':
        await handleSubscriptionCreated(stripeEvent.data.object as Stripe.Subscription)
        break
        
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(stripeEvent.data.object as Stripe.Subscription)
        break
        
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(stripeEvent.data.object as Stripe.Subscription)
        break
        
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(stripeEvent.data.object as Stripe.Invoice)
        break
        
      case 'invoice.payment_failed':
        await handlePaymentFailed(stripeEvent.data.object as Stripe.Invoice)
        break
        
      default:
        console.log(`Unhandled event type: ${stripeEvent.type}`)
    }

    return { received: true }

  } catch (error: any) {
    console.error('Webhook error:', error)
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || 'Webhook processing failed'
    })
  }
})

/**
 * Handle successful checkout completion
 */
async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  try {
    console.log('Processing checkout completion:', session.id)
    
    // Get session metadata
    const metadata = session.metadata
    if (!metadata) {
      console.error('No metadata found in checkout session')
      return
    }

    const { schoolCode, schoolName, adminEmail, adminFirstName, adminLastName, selectedPlan } = metadata

    if (!schoolCode || !schoolName || !adminEmail) {
      console.error('Missing required metadata:', metadata)
      return
    }

    // Create school record
    await createSchoolAccount({
      schoolCode,
      schoolName,
      schoolAddress: metadata.schoolAddress || '',
      adminEmail,
      adminFirstName: adminFirstName || '',
      adminLastName: adminLastName || '',
      selectedPlan,
      stripeCustomerId: session.customer as string,
      stripeSessionId: session.id,
      subscriptionStatus: 'trialing' // Start with trial
    })

    console.log(`School account created successfully: ${schoolCode}`)

  } catch (error) {
    console.error('Error handling checkout completion:', error)
  }
}

/**
 * Handle subscription creation
 */
async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  try {
    console.log('Processing subscription creation:', subscription.id)
    
    // Update school subscription status
    // TODO: Implement database update
    console.log('Subscription created for customer:', subscription.customer)
    
  } catch (error) {
    console.error('Error handling subscription creation:', error)
  }
}

/**
 * Handle subscription updates
 */
async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  try {
    console.log('Processing subscription update:', subscription.id)
    
    // Update school subscription status
    // TODO: Implement database update
    console.log('Subscription updated for customer:', subscription.customer)
    
  } catch (error) {
    console.error('Error handling subscription update:', error)
  }
}

/**
 * Handle subscription deletion
 */
async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  try {
    console.log('Processing subscription deletion:', subscription.id)
    
    // Update school status to cancelled
    // TODO: Implement database update
    console.log('Subscription cancelled for customer:', subscription.customer)
    
  } catch (error) {
    console.error('Error handling subscription deletion:', error)
  }
}

/**
 * Handle successful payment
 */
async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  try {
    console.log('Processing successful payment:', invoice.id)
    
    // Update payment history
    // TODO: Implement database update
    console.log('Payment succeeded for customer:', invoice.customer)
    
  } catch (error) {
    console.error('Error handling payment success:', error)
  }
}

/**
 * Handle failed payment
 */
async function handlePaymentFailed(invoice: Stripe.Invoice) {
  try {
    console.log('Processing failed payment:', invoice.id)
    
    // Handle failed payment (send notifications, etc.)
    // TODO: Implement database update and notifications
    console.log('Payment failed for customer:', invoice.customer)
    
  } catch (error) {
    console.error('Error handling payment failure:', error)
  }
}

/**
 * Create school account in database
 */
async function createSchoolAccount(data: {
  schoolCode: string
  schoolName: string
  schoolAddress: string
  adminEmail: string
  adminFirstName: string
  adminLastName: string
  selectedPlan: string
  stripeCustomerId: string
  stripeSessionId: string
  subscriptionStatus: string
}) {
  // TODO: Implement actual database creation
  // For now, just log the data
  console.log('Creating school account:', data)
  
  // This would typically:
  // 1. Create school record in schools table
  // 2. Create admin user account
  // 3. Link admin to school
  // 4. Set up initial school settings
  // 5. Send welcome email
  
  // Placeholder implementation
  return {
    success: true,
    schoolId: `school_${Date.now()}`,
    adminId: `admin_${Date.now()}`
  }
}
