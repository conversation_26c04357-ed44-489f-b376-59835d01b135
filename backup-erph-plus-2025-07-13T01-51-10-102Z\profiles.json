[{"id": "a86bf35d-b93d-4b26-8cc5-b7bc4feb7c5a", "updated_at": "2025-06-25T05:55:09.714551+00:00", "created_at": "2025-06-25T05:55:09.714551+00:00", "username": null, "full_name": "<PERSON><PERSON><PERSON>", "avatar_url": null, "is_profile_complete": true, "gender": "le<PERSON>i", "role": {"code": "ga", "label": "<PERSON>"}, "class_subjects": [{"class_id": "f5", "className": "5B", "subject_id": "f65c4a0a-eb83-453d-be43-0f2c6ea7744c", "studentCount": 15, "subject_abbreviation": "PJK"}, {"class_id": "f3", "className": "3B", "subject_id": "12619f02-ddf0-46f3-912d-7a08b8748bbd", "studentCount": 18, "subject_abbreviation": "PI"}, {"class_id": "f4", "className": "4A", "subject_id": "a852e975-e44d-4811-aa42-01b2238d834f", "studentCount": 20, "subject_abbreviation": "BKD"}, {"class_id": "f4", "className": "4A", "subject_id": "05639428-ef74-4801-8573-8b0305fa4903", "studentCount": 20, "subject_abbreviation": "BIO"}, {"class_id": "f5", "className": "5B", "subject_id": "2fce500c-995f-43ff-b8ad-d5857e837ef4", "studentCount": 15, "subject_abbreviation": "BC"}, {"class_id": "f5", "className": "5B", "subject_id": "bba818eb-4274-40ca-8c32-b4e96fa644a0", "studentCount": 15, "subject_abbreviation": "BS"}, {"class_id": "f5", "className": "5B", "subject_id": "316a5061-5205-4017-95b4-079c685f2ab1", "studentCount": 15, "subject_abbreviation": "PM"}, {"class_id": "f6", "className": "6 Atas", "subject_id": "8df5f178-b251-4007-9c55-d0f4d10f154a", "studentCount": 6, "subject_abbreviation": "KH"}, {"class_id": "f3", "className": "3B", "subject_id": "8df5f178-b251-4007-9c55-d0f4d10f154a", "studentCount": 18, "subject_abbreviation": "KH"}], "time_slots": [{"id": "1", "label": "7:30 AM - 8:00 AM", "end_time": "08:00", "start_time": "07:30", "period_number": 0}, {"id": "2", "label": "8:00 AM - 8:30 AM", "end_time": "08:30", "start_time": "08:00", "period_number": 1}, {"id": "3", "label": "8:30 AM - 9:00 AM", "end_time": "09:00", "start_time": "08:30", "period_number": 2}, {"id": "4", "label": "9:00 AM - 9:30 AM", "end_time": "09:30", "start_time": "09:00", "period_number": 3}, {"id": "5", "label": "9:30 AM - 10:00 AM", "end_time": "10:00", "start_time": "09:30", "period_number": 4}, {"id": "6", "label": "10:20 AM - 10:50 AM", "end_time": "10:50", "start_time": "10:20", "period_number": 5}, {"id": "7", "label": "10:50 AM - 11:20 AM", "end_time": "11:20", "start_time": "10:50", "period_number": 6}, {"id": "8", "label": "11:20 AM - 11:50 AM", "end_time": "11:50", "start_time": "11:20", "period_number": 7}, {"id": "9", "label": "11:50 AM - 12:20 PM", "end_time": "12:20", "start_time": "11:50", "period_number": 8}, {"id": "10", "label": "1:00 PM - 1:30 PM", "end_time": "13:30", "start_time": "13:00", "period_number": 9}, {"id": "11", "label": "1:30 PM - 2:00 PM", "end_time": "14:00", "start_time": "13:30", "period_number": 10}], "ic_number": null, "teacher_type": null, "religion": null, "date_of_birth": null, "options": [], "academic_qualifications": [], "file_number": null, "spp_reference_number": null, "salary_number": null, "epf_number": null, "income_tax_number": null, "appointment_date": null, "position_confirmation_date": null, "pensionable_position_date": null, "retirement_date": null, "teaching_days_mode": "weekdays"}, {"id": "8e2f23c9-093d-4792-bb58-b69eeee8b397", "updated_at": "2025-06-17T12:29:52.906961+00:00", "created_at": "2025-06-17T12:29:52.906961+00:00", "username": null, "full_name": "<PERSON>", "avatar_url": null, "is_profile_complete": true, "gender": "le<PERSON>i", "role": {"code": "gb", "label": "<PERSON>"}, "class_subjects": [{"class_id": "f3", "className": "3A", "subject_id": "64d7bb72-3333-4a5a-8973-54f454401cbb", "studentCount": 20, "subject_abbreviation": "IBAN"}, {"class_id": "f6", "className": "6 Atas", "subject_id": "a1c161c9-18a5-42b9-8b30-808304782887", "studentCount": 15, "subject_abbreviation": "BI"}, {"class_id": "f1", "className": "1A", "subject_id": "4c3a1e4d-f7cc-4199-b63c-00877b53c08a", "studentCount": 28, "subject_abbreviation": "BI"}, {"class_id": "f1", "className": "1A", "subject_id": "2cb73199-791c-492c-94c9-8384eb1db9a7", "studentCount": 28, "subject_abbreviation": "BJ"}, {"class_id": "f1", "className": "1A", "subject_id": "c8d9c074-0475-4bc2-940c-c7595319ef29", "studentCount": 28, "subject_abbreviation": "RBT"}, {"class_id": "f3", "className": "3A", "subject_id": "5b02e7f1-5e25-402a-be80-35c1ed7655bb", "studentCount": 20, "subject_abbreviation": "PJK"}], "time_slots": [{"id": "1", "label": "7:20 AM - 7:50 AM", "end_time": "07:50", "start_time": "07:20", "period_number": 0}, {"id": "2", "label": "7:50 AM - 8:20 AM", "end_time": "08:20", "start_time": "07:50", "period_number": 1}, {"id": "3", "label": "8:50 AM - 9:20 AM", "end_time": "09:20", "start_time": "08:50", "period_number": 2}, {"id": "4", "label": "9:00 AM - 9:30 AM", "end_time": "09:30", "start_time": "09:00", "period_number": 3}, {"id": "5", "label": "9:30 AM - 10:00 AM", "end_time": "10:00", "start_time": "09:30", "period_number": 4}, {"id": "break1", "label": "Rehat 1", "end_time": "10:20", "start_time": "10:00", "period_number": -1}, {"id": "6", "label": "10:20 AM - 10:50 AM", "end_time": "10:50", "start_time": "10:20", "period_number": 5}, {"id": "7", "label": "10:50 AM - 11:20 AM", "end_time": "11:20", "start_time": "10:50", "period_number": 6}, {"id": "8", "label": "11:20 AM - 11:50 AM", "end_time": "11:50", "start_time": "11:20", "period_number": 7}, {"id": "9", "label": "11:50 AM - 12:20 PM", "end_time": "12:20", "start_time": "11:50", "period_number": 8}, {"id": "break2", "label": "<PERSON><PERSON>", "end_time": "13:00", "start_time": "12:20", "period_number": -2}, {"id": "10", "label": "1:00 PM - 1:30 PM", "end_time": "13:30", "start_time": "13:00", "period_number": 9}, {"id": "11", "label": "1:30 PM - 2:00 PM", "end_time": "14:00", "start_time": "13:30", "period_number": 10}], "ic_number": null, "teacher_type": null, "religion": null, "date_of_birth": null, "options": [], "academic_qualifications": [], "file_number": null, "spp_reference_number": null, "salary_number": null, "epf_number": null, "income_tax_number": null, "appointment_date": null, "position_confirmation_date": null, "pensionable_position_date": null, "retirement_date": null, "teaching_days_mode": "weekdays"}, {"id": "35bf1b3c-8965-4aec-9a36-c5573e952cd2", "updated_at": "2025-07-09T06:34:18.129+00:00", "created_at": "2025-07-01T01:09:14.902644+00:00", "username": null, "full_name": "Test", "avatar_url": null, "is_profile_complete": true, "gender": "le<PERSON>i", "role": {"code": "asdasd", "label": "asdasd"}, "class_subjects": [{"class_id": "t4", "className": "aasd", "subject_id": "dfed2e06-f2ce-4c50-b3ce-44576117d6a1", "studentCount": 23, "subject_abbreviation": "SAI"}, {"class_id": "t1", "className": "asdasd", "subject_id": "0317342e-1554-4155-97f5-94e4864e6eeb", "studentCount": 22, "subject_abbreviation": "PJK"}, {"class_id": "t1", "className": "asdasd", "subject_id": "7f733b27-d2b2-4b5f-a52e-c95f03e2c86c", "studentCount": 22, "subject_abbreviation": "BT"}, {"class_id": "t1", "className": "asdasd", "subject_id": "3289707c-59b1-4779-9ab1-03bbe57dcfd7", "studentCount": 22, "subject_abbreviation": "PM"}], "time_slots": [{"id": "1", "label": "7:20 AM - 7:50 AM", "end_time": "07:50", "start_time": "07:20", "period_number": 0}, {"id": "2", "label": "7:50 AM - 8:20 AM", "end_time": "08:20", "start_time": "07:50", "period_number": 1}, {"id": "3", "label": "8:20 AM - 8:50 AM", "end_time": "08:50", "start_time": "08:20", "period_number": 2}, {"id": "4", "label": "8:50 AM - 9:20 AM", "end_time": "09:20", "start_time": "08:50", "period_number": 3}, {"id": "5", "label": "9:20 AM - 9:50 AM", "end_time": "09:50", "start_time": "09:20", "period_number": 4}, {"id": "6", "label": "9:50 AM - 10:20 AM", "end_time": "10:20", "start_time": "09:50", "period_number": 5}, {"id": "7", "label": "10:20 AM - 10:50 AM", "end_time": "10:50", "start_time": "10:20", "period_number": 6}, {"id": "8", "label": "10:50 AM - 11:20 AM", "end_time": "11:20", "start_time": "10:50", "period_number": 7}, {"id": "9", "label": "11:20 AM - 11:50 AM", "end_time": "11:50", "start_time": "11:20", "period_number": 8}, {"id": "10", "label": "11:50 AM - 12:20 PM", "end_time": "12:20", "start_time": "11:50", "period_number": 9}, {"id": "11", "label": "12:20 PM - 12:50 PM", "end_time": "12:50", "start_time": "12:20", "period_number": 10}, {"id": "12", "label": "12:50 PM - 1:50 PM", "end_time": "13:50", "start_time": "12:50", "period_number": 11}], "ic_number": null, "teacher_type": "asdasd", "religion": null, "date_of_birth": null, "options": [{"id": "1752010036650-epk276xy1", "name": "Bahasa <PERSON>gger<PERSON>", "created_at": "2025-07-08T21:27:16.650Z"}], "academic_qualifications": [{"id": "1751879418018-cjhj3et94", "name": "KPLI", "year": 2011, "created_at": "2025-07-07T09:10:18.018Z", "institution": "IPG Keningau"}], "file_number": "fghfg", "spp_reference_number": "fghfgdh", "salary_number": "gfhfg", "epf_number": "fghfgh", "income_tax_number": "hfghfgh", "appointment_date": null, "position_confirmation_date": null, "pensionable_position_date": null, "retirement_date": null, "teaching_days_mode": "weekdays"}]